import React, { useEffect, useImper<PERSON><PERSON><PERSON>le, useLayoutEffect, useRef } from 'react';
import { AutoSizer, Grid, <PERSON>rid<PERSON><PERSON><PERSON><PERSON><PERSON>, OnScrollParams } from 'react-virtualized';
import { TimelineRow } from '../../interface/action';
import { CommonProp } from '../../interface/common_prop';
import { EditData } from '../../interface/timeline';
import { prefix } from '../../utils/deal_class_prefix';
import { parserTimeToPixel } from '../../utils/deal_data';
import { DragLines } from './drag_lines';
import './edit_area.css';
import { EditRow } from './edit_row';
import { useDragLine } from './hooks/use_drag_line';

export type EditAreaProps = CommonProp & {
  /** Scroll distance from left */
  scrollLeft: number;
  /** Scroll distance from top */
  scrollTop: number;
  /** Scroll callback for synchronizing scroll */
  onScroll: (params: OnScrollParams) => void;
  /** Set editor data */
  setEditorData: (params: TimelineRow[]) => void;
  /** Set scroll left */
  deltaScrollLeft?: (scrollLeft: number) => void;
  /** Ref for accessing the DOM element */
  // ref?: React.RefObject<HTMLDivElement>;
  /** Disable drag operations */
  disableDrag?: boolean;
};

/** Edit area state */
export interface EditAreaState {
  domRef: React.RefObject<HTMLDivElement | null>;
}

export const EditArea = React.forwardRef<EditAreaState, EditAreaProps>(function EditArea(props, ref){
  const {
    editorData = [],
    effects = {},
    rowHeight = 0,
    scaleWidth = 0,
    scaleCount = 0,
    startLeft = 10,
    scrollLeft = 0,
    scrollTop = 0,
    scale = 1,
    hideCursor = false,
    cursorTime = 0,
    //timelineWidth = 0,
    //setScaleCount,
    onScroll,
    dragLine = false,
    getAssistDragLineActionIds,
    onActionMoveEnd,
    onActionMoveStart,
    onActionMoving,
    onActionResizeEnd,
    onActionResizeStart,
    onActionResizing,
    //onClickRow,
    //onDoubleClickRow,
   // onContextMenuRow,
    //onClickAction,
    //onDoubleClickAction,
    //onContextMenuAction,
    //getActionRender,
    //disableDrag = false,
    //setEditorData,
  } = props;
  const { dragLineData, initDragLine, updateDragLine, disposeDragLine, defaultGetAssistPosition, defaultGetMovePosition } = useDragLine();
  const editAreaRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<Grid>(null);
  const heightRef = useRef(-1);

  // ref 数据
  useImperativeHandle(ref, () => ({
    get domRef() {
      return editAreaRef;
    },
  }));

  const handleInitDragLine: EditData['onActionMoveStart'] = (data) => {
    if (dragLine) {
      const assistActionIds =
        getAssistDragLineActionIds &&
        getAssistDragLineActionIds({
          action: data.action,
          row: data.row,
          editorData,
        });
      const cursorLeft = parserTimeToPixel(cursorTime, { scaleWidth, scale, startLeft });
      const assistPositions = defaultGetAssistPosition({
        editorData,
        assistActionIds,
        action: data.action,
        row: data.row,
        scale,
        scaleWidth,
        startLeft,
        hideCursor,
        cursorLeft,
      });
      initDragLine({ assistPositions });
    }
  };

  const handleUpdateDragLine: EditData['onActionMoving'] = (data) => {
    if (dragLine) {
      const movePositions = defaultGetMovePosition({
        ...data,
        startLeft,
        scaleWidth,
        scale,
      });
      updateDragLine({ movePositions });
    }
  };

  /** Get rendering content for each cell */
  const cellRenderer: GridCellRenderer = ({ rowIndex, key, style }) => {
    if (!editorData || rowIndex >= editorData.length) return null;

    const row = editorData[rowIndex]; // Row data
    if (!row) return null;

    return (
      <EditRow
        {...props}
        // CommonProp requirements
        // editorData={editorData}
        //effects={effects}
        //scaleCount={scaleCount}
        // setScaleCount={setScaleCount}
        //cursorTime={cursorTime}
        //timelineWidth={timelineWidth}

        // EditData requirements
        // scale={scale}
        // scaleWidth={scaleWidth}
        //startLeft={startLeft}
        rowHeight={row?.rowHeight || rowHeight}
        // hideCursor={hideCursor}
        //dragLine={dragLine}
        //getAssistDragLineActionIds={getAssistDragLineActionIds}
        //disableDrag={disableDrag}

        // Event handlers
        onActionMoveStart={(data) => {
          handleInitDragLine(data);
          return onActionMoveStart?.(data);
        }}
        onActionResizeStart={(data) => {
          handleInitDragLine(data);
          return onActionResizeStart?.(data);
        }}
        onActionMoving={(data) => {
          handleUpdateDragLine(data);
          return onActionMoving?.(data);
        }}
        onActionResizing={(data) => {
          handleUpdateDragLine(data);
          return onActionResizing?.(data);
        }}
        onActionResizeEnd={(data) => {
          disposeDragLine();
          return onActionResizeEnd?.(data);
        }}
        onActionMoveEnd={(data) => {
          disposeDragLine();
          return onActionMoveEnd?.(data);
        }}
        // onClickRow={onClickRow}
        // onDoubleClickRow={onDoubleClickRow}
        // onContextMenuRow={onContextMenuRow}
        //onClickAction={onClickAction}
        //onDoubleClickAction={onDoubleClickAction}
        // onContextMenuAction={onContextMenuAction}
        //getActionRender={getActionRender}

        // EditRow specific props
        style={{
          ...style,
          backgroundPositionX: `0, ${startLeft}px`,
          backgroundSize: `${startLeft}px, ${scaleWidth}px`,
        }}
        areaRef={editAreaRef}
        key={key}
        rowData={row}
        dragLineData={dragLineData}
        scrollLeft={scrollLeft}
      //setEditorData={setEditorData}
      />
    );
  };

  useLayoutEffect(() => {
    if (gridRef.current) {
      gridRef.current.scrollToPosition({ scrollTop, scrollLeft });
    }
  }, [scrollTop, scrollLeft]);

  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.recomputeGridSize();
    }
  }, [editorData]);

  return (
    <div ref={editAreaRef} className={prefix('edit-area')}>
      <AutoSizer>
        {({ width, height }) => {
          // Get total height
          let totalHeight = 0;
          // Height list
          const heights = editorData.map((row) => {
            const itemHeight = row?.rowHeight || rowHeight;
            totalHeight += itemHeight;
            return itemHeight;
          });
          if (totalHeight < height) {
            heights.push(height - totalHeight);
            if (heightRef.current !== height && heightRef.current >= 0) {
              setTimeout(() => {
                if (gridRef.current) {
                  gridRef.current.recomputeGridSize({
                    rowIndex: heights.length - 1,
                  });
                }
              });
            }
          }
          heightRef.current = height;

          return (
            <Grid
              columnCount={1}
              rowCount={heights.length}
              ref={gridRef}
              cellRenderer={cellRenderer}
              columnWidth={Math.max(scaleCount * scaleWidth + startLeft, width)}
              width={width}
              height={height}
              rowHeight={({ index }) => heights[index] || rowHeight}
              overscanRowCount={10}
              overscanColumnCount={0}
              onScroll={(param) => {
                if (onScroll) {
                  onScroll(param);
                }
              }}
            />
          );
        }}
      </AutoSizer>
      {dragLine && <DragLines scrollLeft={scrollLeft} {...dragLineData} />}
    </div>
  );
});

// Export memoized EditArea component to prevent unnecessary re-renders
//export const EditArea = React.memo(EditAreaComponent);