"use client";

import { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Camera, 
  Maximize2, 
  PictureInPicture2,
  Wifi,
  WifiOff,
  CircleX
} from "lucide-react";
import { cn } from "@/lib/utils";
import { MediaMTXClient } from "@/lib/mediamtx/mediamtx-client";

interface WebRTCPlayerProps {
  streamPath: string;
  isRecording?: boolean;
  onScreenshot?: (timestamp: number) => void;
  currentTime?: number;
  onTimeUpdate?: (currentTime: number) => void;
  isPlaying?: boolean;
  onPlayStateChange?: (playing: boolean) => void;
  className?: string;
  autoPlay?: boolean;
}

interface ConnectionState {
  status: "connecting" | "connected" | "disconnected" | "failed";
  quality: "excellent" | "good" | "poor" | "unknown";
  bitrate?: number;
}

export function WebRTCPlayer({
  streamPath,
  isRecording = false,
  onScreenshot,
  currentTime: externalCurrentTime,
  onTimeUpdate,
  isPlaying: externalIsPlaying,
  onPlayStateChange,
  className,
  autoPlay = true
}: WebRTCPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const pcRef = useRef<RTCPeerConnection | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [internalIsPlaying, setInternalIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState([75]);
  const [showControls, setShowControls] = useState(false);
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    status: "connecting",
    quality: "unknown"
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [internalCurrentTime, setInternalCurrentTime] = useState(0);

  // Use external state if provided, otherwise use internal state
  const currentTime = externalCurrentTime ?? internalCurrentTime;
  const isPlaying = externalIsPlaying ?? internalIsPlaying;
  const mediaClient = new MediaMTXClient();

  // Initialize WebRTC connection
  useEffect(() => {
    let isMounted = true;

    const initializeStream = async () => {
      if (!videoRef.current) return;

      try {
        setConnectionState({ status: "connecting", quality: "unknown" });

        const pc = await mediaClient.createWebRTCConnection(
          streamPath,
          (event) => {
            if (event.streams && event.streams[0] && videoRef.current) {
              videoRef.current.srcObject = event.streams[0];
              if (isMounted) {
                setConnectionState({ status: "connected", quality: "good" });
                if (autoPlay) {
                  videoRef.current.play().catch(console.error);
                }
              }
            }
          }
        );

        if (isMounted) {
          pcRef.current = pc;

          pc.oniceconnectionstatechange = () => {
            if (!isMounted) return;
            const state = pc.iceConnectionState;
            console.log(`ICE connection state: ${state}`);
            
            if (state === "connected" || state === "completed") {
              setConnectionState(prev => ({ ...prev, status: "connected", quality: "good" }));
            } else if (state === "disconnected" || state === "failed") {
              setConnectionState(prev => ({ ...prev, status: "disconnected", quality: "poor" }));
            }
          };

          pc.onconnectionstatechange = () => {
            if (!isMounted) return;
            const state = pc.connectionState;
            console.log(`Connection state: ${state}`);
            
            if (state === "failed") {
              setConnectionState(prev => ({ ...prev, status: "failed", quality: "poor" }));
            }
          };
        }
      } catch (error) {
        console.error("Failed to initialize WebRTC stream:", error);
        if (isMounted) {
          setConnectionState({ status: "failed", quality: "poor" });
        }
      }
    };

    initializeStream();

    return () => {
      isMounted = false;
      if (pcRef.current) {
        pcRef.current.close();
        pcRef.current = null;
      }
    };
  }, [streamPath, autoPlay]);

  // Sync external playing state with internal state
  useEffect(() => {
    if (externalIsPlaying !== undefined && externalIsPlaying !== internalIsPlaying) {
      console.debug(`[WebRTCPlayer] External isPlaying changed: ${externalIsPlaying}, syncing internal state.`);
      setInternalIsPlaying(externalIsPlaying);
    }
  }, [externalIsPlaying, internalIsPlaying]);

  // Sync external current time with video
  useEffect(() => {
    if (externalCurrentTime !== undefined && videoRef.current) {
      const video = videoRef.current;
      const timeDiff = Math.abs(video.currentTime - externalCurrentTime);
      console.debug(`[WebRTCPlayer] External currentTime changed: ${externalCurrentTime}, video currentTime: ${video.currentTime}, timeDiff: ${timeDiff}`);
      // Only seek if there's a significant difference (more than 0.5 seconds)
      if (timeDiff > 0.5) {
        video.currentTime = externalCurrentTime;
      }
    }
  }, []);//externalCurrentTime

  // Handle play/pause synchronization
  useEffect(() => {
    if (!videoRef.current) return;
    
    const video = videoRef.current;
    console.debug(`[WebRTCPlayer] Handling play/pause state: ${isPlaying}`);
    
    if (isPlaying && video.paused) {
      video.play().then(() => {
        console.debug("[WebRTCPlayer] Video playback started successfully");
      }).catch(error => {
        console.error("[WebRTCPlayer] Failed to play video:", error);
        // Notify parent that play failed
        setInternalIsPlaying(false);
        onPlayStateChange?.(false);
      });
    } else if (!isPlaying && !video.paused) {
      video.pause();
      console.debug("[WebRTCPlayer] Video paused");
    }
  }, [isPlaying, onPlayStateChange]);

  // Handle video events to sync back to parent
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => {
      console.debug("[WebRTCPlayer] Video play event");
      if (!internalIsPlaying) {
        setInternalIsPlaying(true);
        onPlayStateChange?.(true);
      }
    };

    const handlePause = () => {
      console.debug("[WebRTCPlayer] Video pause event");
      if (internalIsPlaying) {
        setInternalIsPlaying(false);
        onPlayStateChange?.(false);
      }
    };

    const handleEnded = () => {
      console.debug("[WebRTCPlayer] Video ended event");
      setInternalIsPlaying(false);
      onPlayStateChange?.(false);
    };

    const handleError = () => {
      console.error("[WebRTCPlayer] Video error event");
      setInternalIsPlaying(false);
      onPlayStateChange?.(false);
      setConnectionState({ status: "failed", quality: "poor" });
    };

    // Add event listeners
    video.addEventListener("play", handlePlay);
    video.addEventListener("pause", handlePause);
    video.addEventListener("ended", handleEnded);
    video.addEventListener("error", handleError);

    return () => {
      // Remove event listeners
      video.removeEventListener("play", handlePlay);
      video.removeEventListener("pause", handlePause);
      video.removeEventListener("ended", handleEnded);
      video.removeEventListener("error", handleError);
    };
  }, [internalIsPlaying, onPlayStateChange]);

  // Handle volume changes
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.volume = volume[0] / 100;
      videoRef.current.muted = isMuted;
    }
  }, [volume, isMuted]);


  // Viideo playback handler
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => {
      const time = video.currentTime;
      setInternalCurrentTime(time);
      onTimeUpdate?.(time);
    };

    video.addEventListener("timeupdate", handleTimeUpdate);
    return () => video.removeEventListener("timeupdate", handleTimeUpdate);
  }, [onTimeUpdate]);

  // Fullscreen handler
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  const handlePlayPause = () => {
    if (!videoRef.current) return;
    
    // Toggle the internal state - this will trigger the effect that actually controls the video
    const newPlayState = !internalIsPlaying;
    console.debug(`[WebRTCPlayer] User clicked play/pause, setting state to: ${newPlayState}`);
    
    setInternalIsPlaying(newPlayState);
    onPlayStateChange?.(newPlayState);
  };

  const handleVolumeToggle = () => {
    setIsMuted(!isMuted);
  };

  const handleScreenshot = () => {
    if (!videoRef.current) return;

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;
    ctx.drawImage(videoRef.current, 0, 0);

    const timestamp = Date.now();
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `screenshot-${streamPath}-${timestamp}.png`;
        a.click();
        URL.revokeObjectURL(url);
      }
    });

    onScreenshot?.(timestamp);
  };

  const handleFullscreen = () => {
    if (!containerRef.current) return;

    if (isFullscreen) {
      document.exitFullscreen().catch(console.error);
    } else {
      containerRef.current.requestFullscreen().catch(console.error);
    }
  };

  const handlePictureInPicture = () => {
    if (!videoRef.current) return;

    if (document.pictureInPictureElement) {
      document.exitPictureInPicture().catch(console.error);
    } else {
      videoRef.current.requestPictureInPicture().catch(console.error);
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionState.status) {
      case "connected":
        return <Wifi className="h-4 w-4 text-green-500" />;
      case "connecting":
        return <Wifi className="h-4 w-4 text-yellow-500 animate-pulse" />;
      case "disconnected":
      case "failed":
        return <WifiOff className="h-4 w-4 text-red-500" />;
      default:
        return <CircleX className="h-4 w-4 text-gray-400" />;
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionState.status) {
      case "connected":
        return `Connected • ${connectionState.quality}`;
      case "connecting":
        return "Connecting...";
      case "disconnected":
        return "Disconnected";
      case "failed":
        return "Connection failed";
      default:
        return "Unknown";
    }
  };

  return (
    <div 
      ref={containerRef}
      className={cn(
        "relative bg-black rounded-lg overflow-hidden",
        "aspect-video",
        className
      )}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        playsInline
        muted={isMuted}
      />

      {/* Loading/Error State */}
      {connectionState.status === "connecting" && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-sm">Connecting to stream...</p>
          </div>
        </div>
      )}

      {connectionState.status === "failed" && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <CircleX className="h-8 w-8 mx-auto mb-4 text-red-500" />
            <p className="text-sm">Failed to connect to stream</p>
            <p className="text-xs text-gray-400 mt-1">Check MediaMTX server status</p>
          </div>
        </div>
      )}

      {/* Controls Overlay */}
      <div 
        className={cn(
          "absolute inset-0 bg-gradient-to-t from-black/50 to-transparent",
          "transition-opacity duration-300",
          showControls || connectionState.status !== "connected" ? "opacity-100" : "opacity-0"
        )}
      >
        {/* Top Status Bar */}
        <div className="absolute top-4 left-4 right-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getConnectionStatusIcon()}
            <span className="text-white text-sm">
              {getConnectionStatusText()}
            </span>
          </div>

          <div className="flex items-center gap-2">
            {isRecording && (
              <Badge variant="destructive" className="animate-pulse">
                🔴 LIVE
              </Badge>
            )}
            <Badge variant="outline" className="text-white border-white">
              {streamPath}
            </Badge>
          </div>
        </div>

        {/* Bottom Controls */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="flex items-center gap-2 mb-4">
            {/* Play/Pause (only for evaluation mode) */}
            {!isRecording && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handlePlayPause}
                className="text-white hover:bg-white/20"
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
            )}

            {/* Volume Controls */}
            <Button
              size="sm"
              variant="ghost"
              onClick={handleVolumeToggle}
              className="text-white hover:bg-white/20"
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>

            <div className="w-20">
              <Slider
                value={volume}
                onValueChange={setVolume}
                max={100}
                step={1}
                className="w-full"
              />
            </div>

            <div className="flex-1" />

            {/* Action Buttons */}
            <Button
              size="sm"
              variant="ghost"
              onClick={handleScreenshot}
              className="text-white hover:bg-white/20"
              title="Take Screenshot"
            >
              <Camera className="h-4 w-4" />
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={handlePictureInPicture}
              className="text-white hover:bg-white/20"
              title="Picture in Picture"
            >
              <PictureInPicture2 className="h-4 w-4" />
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={handleFullscreen}
              className="text-white hover:bg-white/20"
              title="Fullscreen"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

