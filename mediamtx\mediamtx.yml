###############################################################################
# MediaMTX Development Configuration for NFM System

# General settings
logLevel: debug
logDestinations: [stdout]
logFile: mediamtx.log

# API settings for management
api: yes
apiAddress: :9997
# Allow path management via API
apiAllowOrigin: "*"

# Metrics
metrics: yes
metricsAddress: :9998

# RTSP server settings
rtsp: yes
rtspTransports: [tcp]
rtspAddress: :8554
rtspEncryption: 'no'
rtspAuthMethods: [basic]

# WebRTC settings for browser streaming
webrtc: yes
webrtcAddress: :8889
webrtcEncryption: no
webrtcAllowOrigin: "*"
webrtcICEServers2: 
    - url: stun:stun.l.google.com:19302

# HLS settings (backup streaming method)
hls: yes
hlsAddress: :8888
hlsAllowOrigin: "*"
hlsSegmentCount: 7
hlsSegmentDuration: 2s
hlsPartDuration: 200ms

# Authentication
authMethod: internal
authInternalUsers:
  # Admin user for publishing streams
  - user: admin
    pass: nfm_dev_2025
    permissions:
      - action: publish
      - action: read
      - action: playback
      - action: api
  # Allow anonymous publish for test_stream only
  - user: any
    permissions:
      - action: publish
        path: test_stream
      - action: read
      - action: playback

# Path defaults
pathDefaults:
  # Recording settings
  record: yes # Disable for development to save disk space
  recordPath: ./recordings/%path/%Y-%m-%d_%H-%M-%S
  recordFormat: fmp4
  recordPartDuration: 1s
  recordSegmentDuration: 1h

# Path configurations
paths:
  # Test stream for development (using ffmpeg test source)
  # added -re flag to simulate real-time input
  test_stream:
    #runOnDemand: >
    #  ffmpeg -re -f lavfi -i testsrc2=size=1920x1080:rate=30
    #  -f lavfi -i sine=frequency=1000
    #  -c:v libx264 -preset ultrafast -b:v 3000k
    #  -c:a aac -b:a 128k
    #  -f rtsp rtsp://localhost:8554/test_stream

    # Optional: Restart the command if it fails
    #runOnDemandRestart: yes

      # Optional but recommended: Stop the command 10s after the last client disconnects
    #runOnDemandCloseAfter: 10s
    runOnInit: >
      ffmpeg -re -f lavfi -i testsrc2=size=1920x1080:rate=30
      -f lavfi -i sine=frequency=1000
      -c:v libx264 -preset ultrafast -b:v 3000k
      -c:a aac -b:a 128k
      -f rtsp rtsp://localhost:8554/test_stream
    runOnInitRestart: yes

  # Inomed Main Stream (OR1)
  or1_main:
    source: rtsp://*************:8554/main/av
    rtspTransport: tcp
    sourceOnDemand: yes
    
  # Inomed Sub Stream (OR1 - Lower Quality)
  or1_sub:
    source: rtsp://*************:8554/sub/av
    rtspTransport: tcp
    sourceOnDemand: yes
    
  # Additional OR streams can be added here
  or2_main:
    source: rtsp://*************:8554/main/av
    rtspTransport: tcp
    sourceOnDemand: yes
    
  # Generic path prefix for dynamic streams
  # Using simple path without regex
  stream_dynamic:
    # Will be configured at runtime
    runOnInit: echo "Dynamic stream path ready"