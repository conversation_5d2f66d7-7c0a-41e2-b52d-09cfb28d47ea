"use client";

import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuLabel } from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';

import { cn } from '@/lib/utils';
import { useProjectContext } from '@/components/contexts/ProjectContext';
import { useVideoTimeline } from '@/components/contexts/VideoTimelineContext';
import { Plus, AlertTriangle, Activity, Zap, Settings, ChevronLeft, ChevronRight } from 'lucide-react';
import { toast } from 'sonner';
import { useEventCreation } from '@/hooks/useEventCreation';
import { EventEditForm } from './EventEditForm';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

interface EventCreationBarProps {
  className?: string;
  disabled?: boolean;
  showCustomButton?: boolean;
  focusMode?: boolean;
  layout?: 'vertical' | 'horizontal';
}

interface EventButtonConfig {
  modalityId: string;
  modalityName: string;
  eventTypeId: string;
  label: string;
  severity: "normal" | "warning" | "critical";
  color: string;
  icon: React.ReactNode;
  description?: string;
}

export function EventCreationBar({
  className,
  disabled = false,
  showCustomButton = false, // Disabled by default now
  focusMode = false,
  layout = 'vertical'
}: EventCreationBarProps) {
  // Get project context and video timeline state
  const { currentProject, modalities, currentUserId } = useProjectContext();
  const { currentTime } = useVideoTimeline();
  const { createEvent, isCreating, error, clearError } = useEventCreation();

  // Fetch all event types with their modality information
  const eventTypes = useQuery(api.eventTypes.getAllEventTypes);

  // State for edit form and minimize
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  // Generate default event buttons (one per modality showing default eventType)
  const defaultEventButtons = useMemo<EventButtonConfig[]>(() => {
    if (!eventTypes || !modalities || eventTypes.length === 0 || modalities.length === 0) return [];

    const buttons: EventButtonConfig[] = [];

    // Group eventTypes by modality
    const eventTypesByModality = eventTypes.reduce((acc, eventType) => {
      const modalityId = eventType.modalityId;
      if (!acc[modalityId]) {
        acc[modalityId] = [];
      }
      acc[modalityId].push(eventType);
      return acc;
    }, {} as Record<string, typeof eventTypes>);

    modalities.forEach(modality => {
      // Skip the "ALL" modality for event creation
      if (modality.name === 'ALL') return;

      const modalityEventTypes = eventTypesByModality[modality._id];
      if (!modalityEventTypes || modalityEventTypes.length === 0) return;

      // Get the default (first) event type for this modality
      const defaultEventType = modalityEventTypes[0];

      // Determine icon based on severity
      let icon: React.ReactNode;
      switch (defaultEventType.severity) {
        case 'critical':
          icon = <AlertTriangle className="h-4 w-4" />;
          break;
        case 'warning':
          icon = <Activity className="h-4 w-4" />;
          break;
        case 'normal':
          icon = <Zap className="h-4 w-4" />;
          break;
        default:
          icon = <Plus className="h-4 w-4" />;
      }

      buttons.push({
        modalityId: modality._id,
        modalityName: modality.name,
        eventTypeId: defaultEventType._id,
        label: defaultEventType.name, // Show eventType name, not modality name
        severity: defaultEventType.severity,
        color: modality.colorCode,
        icon,
        description: defaultEventType.description
      });
    });

    return buttons;
  }, [eventTypes, modalities]);

  // Group all eventTypes by modality for context menus
  const eventTypesByModality = useMemo(() => {
    if (!eventTypes) return {};

    return eventTypes.reduce((acc, eventType) => {
      const modalityId = eventType.modalityId;
      if (!acc[modalityId]) {
        acc[modalityId] = [];
      }
      acc[modalityId].push(eventType);
      return acc;
    }, {} as Record<string, typeof eventTypes>);
  }, [eventTypes]);

  // Handle edit event
  const handleEditEvent = useCallback((eventId: string) => {
    setEditingEventId(eventId);
    setIsEditFormOpen(true);
  }, []);

  // Handle edit form close
  const handleEditFormClose = useCallback(() => {
    setIsEditFormOpen(false);
    setEditingEventId(null);
  }, []);

  // Handle edit form save
  const handleEditFormSave = useCallback((_eventId: string) => {
    toast.success('Event updated successfully');
    // Form will close automatically
  }, []);

  // Handle edit form delete
  const handleEditFormDelete = useCallback((_eventId: string) => {
    toast.success('Event deleted successfully');
    // Form will close automatically
  }, []);

  // Handle event creation with enhanced toast notifications
  const handleCreateEvent = useCallback(async (buttonConfig: EventButtonConfig, overrideSeverity?: "normal" | "warning" | "critical") => {
    if (!currentProject || !currentUserId) {
      toast.error('Project or user not available');
      return;
    }

    try {
      const eventId = await createEvent({
        modalityId: buttonConfig.modalityId as Id<"modalityConfigs">,
        eventTypeId: buttonConfig.eventTypeId as Id<"eventTypes">,
        startTime: currentTime,
        title: buttonConfig.label,
        description: buttonConfig.description || `${buttonConfig.modalityName} event created at ${Math.floor(currentTime)}s`
      });

      // Enhanced toast with edit button
      if (eventId) {
        toast.success(`${buttonConfig.label} created successfully`, {
          description: `Severity: ${buttonConfig.severity} • Time: ${Math.floor(currentTime)}s`,
          action: {
            label: "Edit",
            onClick: () => handleEditEvent(eventId)
          },
          duration: 5000
        });
      } else {
        toast.success(`${buttonConfig.label} created successfully`);
      }
    } catch (error) {
      console.error('Failed to create event:', error);
      toast.error(`Failed to create ${buttonConfig.label}`);
    }
  }, [currentProject, currentUserId, currentTime, createEvent, handleEditEvent]);



  // Clear error when component unmounts or error changes
  React.useEffect(() => {
    if (error) {
      const timer = setTimeout(clearError, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Don't render if no project or modalities
  if (!currentProject || !modalities || modalities.length === 0) {
    return null;
  }

  // Create placeholder buttons for missing functionality
  const placeholderButtons = [
    {
      id: 'note',
      label: 'Note',
      icon: <Plus className="h-4 w-4" />,
      severity: 'normal' as const,
      description: 'Add general note'
    },
    {
      id: 'warning',
      label: 'Warning',
      icon: <AlertTriangle className="h-4 w-4" />,
      severity: 'warning' as const,
      description: 'General warning'
    },
    {
      id: 'review',
      label: 'Review',
      icon: <Activity className="h-4 w-4" />,
      severity: 'normal' as const,
      description: 'Mark for review'
    }
  ];

  return (
    <Card className={cn(
      "transition-all duration-200",
      layout === 'vertical' ? "h-full flex flex-col" : "h-fit",
      // Minimize functionality - reduce width in vertical layout
      layout === 'vertical' && isMinimized ? "w-20" : "w-full",
      className
    )}>
      {!focusMode && (
        <CardHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <CardTitle className={cn(
              "font-semibold transition-all duration-200",
              isMinimized ? "text-sm" : "text-lg"
            )}>
              {isMinimized ? "Events" : "Event Creation"}
            </CardTitle>
            <div className="flex items-center gap-1">
              {/* Minimize/Expand Button (only in vertical layout) */}
              {layout === 'vertical' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="h-8 w-8 p-0"
                  title={isMinimized ? "Expand Event Creation" : "Minimize Event Creation"}
                >
                  {isMinimized ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
                </Button>
              )}

              {/* Settings Button */}
              {!isMinimized && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  title="Configure Default Event Types"
                >
                  <Settings className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
          {error && !isMinimized && (
            <div className="p-2 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
        </CardHeader>
      )}

      <CardContent className={cn(
        layout === 'vertical' ? "flex-1 overflow-y-auto space-y-2" : "space-y-2",
        "px-3",
        isMinimized && layout === 'vertical' && "px-1" // Reduced padding when minimized
      )}>
        {!focusMode && !isMinimized && isCreating && (
          <div className="flex justify-center">
            <Badge variant="secondary" className="animate-pulse">
              Creating...
            </Badge>
          </div>
        )}

        {/* Event Type Buttons */}
        <div className={cn(
          "gap-2",
          layout === 'horizontal'
            ? "flex flex-row overflow-x-auto pb-1"
            : "flex flex-col"
        )}>
          {/* Modality-based EventType Buttons */}
          {modalities
            .filter(modality => modality.name !== 'ALL')
            .map(modality => {
              const modalityEventTypes = eventTypesByModality[modality._id] || [];
              if (modalityEventTypes.length === 0) return null;

              const defaultEventType = modalityEventTypes[0];
              const getSeverityIcon = (severity: string) => {
                switch (severity) {
                  case 'critical': return AlertTriangle;
                  case 'warning': return Activity;
                  case 'normal': return Zap;
                  default: return Plus;
                }
              };

              const SeverityIcon = getSeverityIcon(defaultEventType.severity);

              return (
                <DropdownMenu key={modality._id}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={disabled || isCreating}
                      onClick={() => {
                        // Left click creates default eventType
                        handleCreateEvent({
                          modalityId: modality._id,
                          modalityName: modality.name,
                          eventTypeId: defaultEventType._id,
                          label: defaultEventType.name,
                          severity: defaultEventType.severity,
                          color: modality.colorCode,
                          icon: <SeverityIcon className="h-4 w-4" />,
                          description: defaultEventType.description
                        });
                      }}
                      onContextMenu={(e) => {
                        e.preventDefault();
                        // Right click should open the dropdown
                        const trigger = e.currentTarget;
                        trigger.click(); // Programmatically open dropdown
                      }}
                      className={cn(
                        "flex items-center gap-2 justify-start text-left transition-all duration-200",
                        "rounded-sm border hover:shadow-sm",
                        // Layout and minimize-specific sizing
                        layout === 'horizontal'
                          ? "h-10 px-2 flex-shrink-0 min-w-[60px]"
                          : isMinimized
                            ? "h-12 px-1 w-full justify-center" // Minimized: just abbreviation, centered
                            : "h-15 px-3 w-full", // Full: abbreviation + eventType info
                        // Severity-based styling
                        defaultEventType.severity === 'critical' && "border-red-200 hover:bg-red-50",
                        defaultEventType.severity === 'warning' && "border-yellow-200 hover:bg-yellow-50",
                        defaultEventType.severity === 'normal' && "border-green-200 hover:bg-green-50"
                      )}
                      title={`Left-click: Create ${defaultEventType.name} | Right-click: More options`}
                    >
                      {isMinimized && layout === 'vertical' ? (
                        /* Minimized: Only modality abbreviation centered */
                        <div className="flex flex-col items-center gap-1">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: modality.colorCode }}
                          />
                          <span className="text-xs font-bold uppercase tracking-wide">
                            {modality.name}
                          </span>
                        </div>
                      ) : (
                        /* Full layout: Modality + EventType info */
                        <>
                          {/* Modality color + abbreviation */}
                          <div
                            className="w-2 h-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: modality.colorCode }}
                          />
                          <span className={cn(
                            "font-medium uppercase tracking-wide",
                            layout === 'horizontal' ? "text-xs" : "text-sm"
                          )}>
                            {modality.name}
                          </span>

                          {/* EventType info (only in vertical layout when not minimized) */}
                          {layout === 'vertical' && (
                            <div className="flex-1 min-w-0 ml-2">
                              <div className="flex items-center gap-2">
                                <SeverityIcon className={cn(
                                  "h-3 w-3 flex-shrink-0",
                                  defaultEventType.severity === 'critical' && "text-red-600",
                                  defaultEventType.severity === 'warning' && "text-yellow-600",
                                  defaultEventType.severity === 'normal' && "text-green-600"
                                )} />
                                <span className="text-sm font-medium truncate">{defaultEventType.name}</span>
                              </div>
                              {defaultEventType.description && (
                                <div className="text-xs text-muted-foreground truncate mt-0.5">
                                  {defaultEventType.description}
                                </div>
                              )}
                            </div>
                          )}
                        </>
                      )}
                    </Button>
                  </DropdownMenuTrigger>

                  <DropdownMenuContent align="start" className="w-64">
                    <DropdownMenuLabel className="flex items-center gap-2">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: modality.colorCode }}
                      />
                      {modality.displayName} Events
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    {modalityEventTypes.map((eventType) => {
                      const getSeverityIcon = (severity: string) => {
                        switch (severity) {
                          case 'critical': return AlertTriangle;
                          case 'warning': return Activity;
                          case 'normal': return Zap;
                          default: return Plus;
                        }
                      };

                      const SeverityIcon = getSeverityIcon(eventType.severity);

                      return (
                        <DropdownMenuItem
                          key={eventType._id}
                          onClick={() => handleCreateEvent({
                            modalityId: modality._id,
                            modalityName: modality.name,
                            eventTypeId: eventType._id,
                            label: eventType.name,
                            severity: eventType.severity,
                            color: modality.colorCode,
                            icon: <SeverityIcon className="h-4 w-4" />,
                            description: eventType.description
                          })}
                          className="flex items-center gap-3 cursor-pointer"
                        >
                          <SeverityIcon className={cn(
                            "h-4 w-4",
                            eventType.severity === 'critical' && "text-red-600",
                            eventType.severity === 'warning' && "text-yellow-600",
                            eventType.severity === 'normal' && "text-green-600"
                          )} />
                          <div className="flex-1">
                            <div className="font-medium">{eventType.name}</div>
                            {eventType.description && (
                              <div className="text-xs text-muted-foreground">{eventType.description}</div>
                            )}
                          </div>
                          <Badge
                            variant="secondary"
                            className={cn(
                              "text-xs",
                              eventType.severity === 'critical' && "bg-red-100 text-red-700",
                              eventType.severity === 'warning' && "bg-yellow-100 text-yellow-700",
                              eventType.severity === 'normal' && "bg-green-100 text-green-700"
                            )}
                          >
                            {eventType.severity}
                          </Badge>
                        </DropdownMenuItem>
                      );
                    })}
                  </DropdownMenuContent>
                </DropdownMenu>
              );
            })}
        </div>

        {/* Separator and Placeholder Buttons */}
        {!isMinimized && (
          <>
            <Separator />

            {/* Placeholder Buttons - Horizontal in vertical layout */}
            <div className={cn(
              "gap-2",
              layout === 'vertical'
                ? "flex flex-row" // Horizontal row in vertical layout
                : "flex flex-row" // Always horizontal for placeholders
            )}>
              {placeholderButtons.map((button) => (
            <Button
              key={button.id}
              variant="outline"
              size="sm"
              disabled={disabled || isCreating}
              onClick={() => {
                toast.info(`${button.label} functionality coming soon`);
              }}
              className={cn(
                "flex items-center gap-1 justify-center text-center transition-all duration-200",
                "rounded-sm border hover:shadow-sm border-dashed",
                // Smaller buttons for placeholders - always horizontal
                "h-8 px-2 flex-1",
                // Severity-based styling
                button.severity === 'warning' && "border-yellow-200 hover:bg-yellow-50",
                button.severity === 'normal' && "border-gray-200 hover:bg-gray-50"
              )}
              title={button.description}
            >
              <div className="w-2 h-2 rounded-full bg-gray-400 flex-shrink-0" />
              <span className="text-xs font-medium uppercase tracking-wide text-muted-foreground">
                {button.label}
              </span>
            </Button>
          ))}
            </div>
          </>
        )}
      </CardContent>

      {/* Event Edit Form */}
      <EventEditForm
        eventId={editingEventId}
        isOpen={isEditFormOpen}
        onClose={handleEditFormClose}
        onSave={handleEditFormSave}
        onDelete={handleEditFormDelete}
      />
    </Card>
  );
}
