import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { MultiSelect } from '@/components/ui_custom/multi-select'
import { ZoomIn, ZoomOut, RotateCcw, SkipForward, SkipBack, Expand, ChevronLeft, ChevronRight } from 'lucide-react'
import { useTimelineScale, formatTimePosition } from '@/hooks/useTimelineScale'
import { TimelineRuler } from './TimelineRuler'
import { CurrentTimeIndicator } from './CurrentTimeIndicator'
import { ModalityTrack } from './ModalityTrack'
import { TimelineEvent, TimelineModality } from '@/types/timeline'
import { toast } from "sonner"

export interface TimelineContainerProps {
  // Video sync
  currentTime?: number
  duration?: number
  onSeek?: (time: number) => void
  
  // Event data from backend
  events?: TimelineEvent[]
  modalities?: TimelineModality[]
  
  // Callbacks
  onEventClick?: (event: TimelineEvent) => void
  onCreateEvent?: (time: number, modalityId?: string) => void
  onEventEdit?: (event: TimelineEvent) => void
  onEventDelete?: (event: TimelineEvent) => void
  onModalityVisibilityChange?: (modalityIds: string[]) => void
  
  // Display options
  className?: string
  height?: number
}

export function TimelineContainer({
  currentTime = 0,
  duration = 3600,
  onSeek,
  events = [],
  modalities = [],
  onEventClick,
  onCreateEvent,
  onEventEdit,
  onEventDelete,
  onModalityVisibilityChange,
  className,
  height = 300
}: TimelineContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isExpanded, setIsExpanded] = useState(false)
  const [viewportWidth, setViewportWidth] = useState(800)

  // Initialize timeline scale hook
  const timeline = useTimelineScale(duration, 60)
  
  // Get visible modalities and ALL modality
  const visibleModalities = modalities.filter(m => m.isVisible && m.name !== 'ALL')
  const allModality = modalities.find(m => m.name === 'ALL')



  // Handler functions
  const handleTimelineSeek = useCallback((time: number) => {
    timeline.setCurrentTime(time)
    onSeek?.(time)
  }, [timeline, onSeek])

  const handleScrollLeft = useCallback(() => {
    const scrollAmount = viewportWidth * 0.25
    const newScrollLeft = Math.max(0, timeline.scrollLeft - scrollAmount)
    timeline.setScrollLeft(newScrollLeft)
  }, [viewportWidth, timeline])

  const handleScrollRight = useCallback(() => {
    const scrollAmount = viewportWidth * 0.25
    const newScrollLeft = Math.min(timeline.maxScrollLeft, timeline.scrollLeft + scrollAmount)
    timeline.setScrollLeft(newScrollLeft)
  }, [viewportWidth, timeline])

  const handleNextEvent = useCallback(() => {
    const futureEvents = events.filter(event => event.startTime > currentTime)
    if (futureEvents.length > 0) {
      const nextEvent = futureEvents.reduce((closest, event) => 
        event.startTime < closest.startTime ? event : closest
      )
      timeline.scrollToTime(nextEvent.startTime)
      handleTimelineSeek(nextEvent.startTime)
    } else {
      toast.info("No upcoming events")
    }
  }, [events, currentTime, timeline, handleTimelineSeek])

  const handlePrevEvent = useCallback(() => {
    const pastEvents = events.filter(event => event.startTime < currentTime)
    if (pastEvents.length > 0) {
      const prevEvent = pastEvents.reduce((closest, event) => 
        event.startTime > closest.startTime ? event : closest
      )
      timeline.scrollToTime(prevEvent.startTime)
      handleTimelineSeek(prevEvent.startTime)
    } else {
      toast.info("No previous events")
    }
  }, [events, currentTime, timeline, handleTimelineSeek])

  const handleModalityNavigate = useCallback((modalityId: string, direction: 'prev' | 'next') => {
    const modalityEvents = events
      .filter(e => e.modalityId === modalityId)
      .sort((a, b) => a.startTime - b.startTime)
    
    let targetEvent
    if (direction === 'prev') {
      targetEvent = modalityEvents.filter(e => e.startTime < currentTime).pop()
    } else {
      targetEvent = modalityEvents.find(e => e.startTime > currentTime)
    }
    
    if (targetEvent) {
      timeline.scrollToTime(targetEvent.startTime)
      handleTimelineSeek(targetEvent.startTime)
    } else {
      const modality = modalities.find(m => m._id === modalityId)
      toast.info(`No ${direction === 'prev' ? 'previous' : 'upcoming'} ${modality?.name || 'events'}`)
    }
  }, [events, currentTime, timeline, handleTimelineSeek, modalities])



  // Update timeline state when props change
  useEffect(() => {
    timeline.setCurrentTime(currentTime)
  }, [currentTime, timeline])

  useEffect(() => {
    timeline.setDuration(duration)
  }, [duration, timeline])

  useEffect(() => {
    timeline.setViewportWidth(viewportWidth)
  }, [viewportWidth, timeline])

  // Handle container resize
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        // Account for the modality label area (ml-16 = 64px) and some padding
        const width = containerRef.current.clientWidth - 64 - 16
        setViewportWidth(Math.max(200, width)) // Ensure minimum width
      }
    }

    // Initial width calculation with a small delay to ensure DOM is ready
    const timeoutId = setTimeout(updateWidth, 0)
    updateWidth()
    window.addEventListener('resize', updateWidth)
    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', updateWidth)
    }
  }, [])

  // Handle keyboard and mouse events
  useEffect(() => {
    const containerElement = containerRef.current
    if (!containerElement) return

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target !== document.body) return
      
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          handleTimelineSeek(Math.max(0, currentTime - 5))
          break
        case 'ArrowRight':
          event.preventDefault()
          handleTimelineSeek(Math.min(duration, currentTime + 5))
          break
      }
    }

    const handleWheel = (event: WheelEvent) => {
      if (!containerElement.contains(event.target as Node)) return
      
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        const zoomSensitivity = 30
        if (Math.abs(event.deltaY) > zoomSensitivity) {
          if (event.deltaY < -zoomSensitivity) {
            timeline.zoomIn()
          } else if (event.deltaY > zoomSensitivity) {
            timeline.zoomOut()
          }
        }
      } else {
        event.preventDefault()
        const scrollSensitivity = 0.5
        const scrollDelta = event.deltaY * scrollSensitivity
        const newScrollLeft = Math.max(0, Math.min(timeline.scrollLeft + scrollDelta, timeline.maxScrollLeft))
        timeline.setScrollLeft(newScrollLeft)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    containerElement.addEventListener('wheel', handleWheel, { passive: false })
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      containerElement.removeEventListener('wheel', handleWheel)
    }
  }, [currentTime, duration, timeline, handleTimelineSeek])

  const totalTrackHeight = isExpanded 
    ? visibleModalities.length * 40 + 48
    : 60 + 48

  return (
    <div 
      ref={containerRef}
      className={cn(
        "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",
        className
      )}
      style={{ height: height }}
    >
      {/* Timeline Controls Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleScrollLeft}
            disabled={timeline.scrollLeft <= 0}
            title="Scroll Left"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleScrollRight}
            disabled={timeline.scrollLeft >= timeline.maxScrollLeft}
            title="Scroll Right"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={timeline.zoomOut}
            disabled={timeline.scale === 300}
            title="Zoom Out"
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={timeline.zoomIn}
            disabled={timeline.scale === 15}
            title="Zoom In"
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={timeline.fitToView}
            title="Fit to View"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrevEvent}
            disabled={events.length === 0}
            title="Previous Event"
          >
            <SkipBack className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleNextEvent}
            disabled={events.length === 0}
            title="Next Event"
          >
            <SkipForward className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            title={isExpanded ? "Compact View" : "Expanded View"}
          >
            <Expand className="h-4 w-4" />
          </Button>
          
          {/* Modality Filter Multi-Select */}
          <MultiSelect
            options={modalities.map(modality => ({
              label: modality.name,
              value: modality._id,
              icon: () => (
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: modality.colorCode }}
                />
              )
            }))}
            onValueChange={(selectedIds) => onModalityVisibilityChange?.(selectedIds)}
            defaultValue={modalities.filter(m => m.isVisible).map(m => m._id)}
            placeholder="Select modalities..."
            variant="default"
            animation={2}
            maxCount={5}
            className="w-64 max-w-xs"
          />
        </div>
        
        <div className="flex items-center gap-4 text-sm font-mono text-gray-600 dark:text-gray-300">
          <div>
            ⏰ {formatTimePosition(currentTime)} / {formatTimePosition(duration)}
          </div>
          <div className="flex items-center gap-1">
            <span className="text-xs">Events:</span>
            <span className="font-semibold">{events.length}</span>
          </div>
        </div>
      </div>

      {/* Timeline Content */}
      <div className="relative overflow-hidden" style={{ height: totalTrackHeight }} data-timeline-container>
        {/* Timeline Ruler */}
        <TimelineRuler
          timeline={timeline}
          onTimeClick={handleTimelineSeek}
        />

        {/* Timeline Tracks */}
        <div className="relative">
          {isExpanded ? (
            visibleModalities.map((modality) => (
              <ModalityTrack
                key={modality._id}
                modality={modality}
                timeline={timeline}
                events={events}
                isExpanded={true}
                onEventClick={onEventClick}
                onTrackClick={(time) => onCreateEvent?.(time, modality._id)}
                onEventEdit={onEventEdit}
                onEventDelete={onEventDelete}
                onModalityNavigate={handleModalityNavigate}
              />
            ))
          ) : allModality ? (
            <ModalityTrack
              modality={{
                ...allModality,
                isVisible: true
              }}
              timeline={timeline}
              events={events}
              isExpanded={false}
              onEventClick={onEventClick}
              onTrackClick={(time) => onCreateEvent?.(time, allModality._id)}
              onEventEdit={onEventEdit}
              onEventDelete={onEventDelete}
              onModalityNavigate={handleModalityNavigate}
            />
          ) : (
            <div className="h-15 flex items-center justify-center text-muted-foreground">
              No ALL modality found. Please seed the database.
            </div>
          )}
        </div>

        {/* Current Time Indicator */}
        <CurrentTimeIndicator
          timeline={timeline}
          height={totalTrackHeight}
          onSeek={handleTimelineSeek}
          onScrub={handleTimelineSeek}
        />
      </div>
    </div>
  )
}
