import { Doc } from "@/convex/_generated/dataModel";

// Export types based on Convex schema - use these directly
export type ModalityConfig = Doc<"modalityConfigs">;
export type MonitoringEvent = Doc<"monitoringEvents">;
export type Project = Doc<"projects">;
export type User = Doc<"users">;
export type StreamSession = Doc<"streamSessions">;

// Timeline-specific types - use the unified TimelineEvent from components/timeline/interface/timeline
// Re-export for convenience
export type { TimelineEvent } from '@/components/timeline/interface/timeline';

export type TimelineModality = ModalityConfig & {
  isVisible: boolean;
};
