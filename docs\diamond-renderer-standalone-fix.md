# Diamond Renderer Standalone Fix

## Problem

The diamond renderer was showing alongside the default bars instead of replacing them completely. The issues were:

1. **Bars still visible** - Default timeline bars were rendering behind/alongside diamonds
2. **Poor positioning** - Diamonds were not properly centered horizontally and vertically
3. **CSS conflicts** - CSS `!important` rules were overriding inline styles
4. **Class conflicts** - Timeline engine classes were triggering default rendering

## Root Cause Analysis

### 1. **CSS Override Issues**
```css
/* BEFORE: CSS was overriding inline styles */
.timeline-action-diamond {
  width: 20px !important;
  height: 20px !important;
  top: 2px !important;
  left: auto !important;
}
```

### 2. **Class Conflicts**
```typescript
// BEFORE: These classes might trigger default timeline rendering
className={cn(
  'timeline-action',           // ← Might trigger default bar rendering
  'timeline-editor-action',    // ← Might conflict with engine
  'timeline-action-diamond'
)}
```

### 3. **Positioning Issues**
```typescript
// BEFORE: Not properly centered
const leftPosition = action.start * scale;  // ← Wrong calculation
const topPosition = (height - 20) / 2;     // ← Not accounting for diamond size
```

## Solution

### 1. **Standalone Diamond Renderer**

Created a completely independent diamond renderer that doesn't rely on BaseActionRenderer:

```typescript
export function DiamondEventRenderer(props: NFMActionRendererProps) {
  const { action, row, isSelected, isHovered, scale, height } = props;
  
  // Get modality color
  const modalityColor = row.colorCode || action.modalityColor || '#6b7280';
  const modalityColorDark = adjustColorBrightness(modalityColor, -20);
  
  // Get severity for content
  const severity = action.severity || 'normal';
  
  // Proper positioning calculation
  const { left: leftPosition } = parserTimeToTransform(
    { start: action.start, end: action.start },
    { startLeft: props.startLeft || 10, scale, scaleWidth: props.scaleWidth || 100 }
  );
  
  // Center the diamond both horizontally and vertically
  const diamondSize = 16;
  const adjustedLeft = leftPosition - (diamondSize / 2); // Center on start time
  const adjustedTop = (height - diamondSize) / 2;       // Center in row
  
  return (
    <div
      data-action-id={action.id}
      className={cn(
        // Minimal classes to avoid conflicts
        'timeline-action-diamond',
        'absolute cursor-pointer transition-all duration-200',
        'flex items-center justify-center text-xs font-bold text-white',
        'hover:shadow-lg',
        // Selection and severity states
        { 'ring-2 ring-blue-300 ring-offset-1': isSelected },
        { 'brightness-110': isHovered },
        { 'timeline-action-critical': severity === 'critical' },
        { 'timeline-action-warning': severity === 'warning' }
      )}
      style={{
        width: `${diamondSize}px`,
        height: `${diamondSize}px`,
        backgroundColor: modalityColor,
        borderColor: modalityColorDark,
        border: `2px solid ${modalityColorDark}`,
        borderRadius: '3px',
        transform: 'rotate(45deg)',
        left: `${adjustedLeft}px`,
        top: `${adjustedTop}px`,
        zIndex: 20, // Ensure it appears above any default bars
      }}
      title={`${action.title || 'Event'} (${action.start.toFixed(1)}s)`}
    >
      <div 
        className="timeline-action-content"
        style={{
          transform: 'rotate(-45deg)', // Counter-rotate content
          fontSize: '8px',
          fontWeight: 'bold',
          lineHeight: '1',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {severity === 'critical' ? '!' :
         severity === 'warning' ? '⚠' :
         action.title?.charAt(0) || 'E'}
      </div>
    </div>
  );
}
```

### 2. **Updated CSS**

Removed `!important` rules that were overriding inline styles:

```css
/* AFTER: Allow inline style control */
.timeline-action-diamond {
  /* Size and positioning are now handled by inline styles */
  transform: rotate(45deg);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center;
  margin: 0;
  position: absolute;
  z-index: 10; /* Ensure diamond appears above any default bars */
}

.timeline-action-diamond .timeline-action-content {
  transform: rotate(-45deg);
  font-size: 8px;
  font-weight: bold;
  line-height: 1;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}
```

### 3. **Proper Positioning**

- **Horizontal centering**: `leftPosition - (diamondSize / 2)` centers the diamond on the event start time
- **Vertical centering**: `(height - diamondSize) / 2` centers the diamond in the row
- **Proper timeline calculation**: Uses `parserTimeToTransform` for accurate positioning

### 4. **Minimal Class Usage**

Removed classes that might trigger default timeline rendering:
- ❌ Removed `timeline-action` (might trigger default bars)
- ❌ Removed `timeline-editor-action` (might conflict with engine)
- ✅ Kept `timeline-action-diamond` (for our custom styling)
- ✅ Added `zIndex: 20` to ensure diamond appears above any default elements

## Expected Results

### Visual Changes
- ✅ **No bars visible** for small events - only diamond shapes
- ✅ **Proper centering** - diamonds centered on event start time and in row
- ✅ **Correct size** - 16px diamonds that are clearly visible
- ✅ **Proper colors** - using modality colors from database
- ✅ **Severity indicators** - showing !, ⚠, or first letter

### Console Output
```
[getActionRenderer] Event: abc123 Start: 10 End: 25 PixelWidth: 25
[getActionRenderer] Using DiamondEventRenderer for short event
[DiamondEventRenderer] Rendering standalone diamond for event: abc123 at position: 95 12
```

## Testing Instructions

### 1. **Create Short Events**
- Create events with duration < 0.3 seconds at scale 100
- Should see diamond only, no bars

### 2. **Test Positioning**
- Diamonds should be centered on event start time
- Diamonds should be vertically centered in rows

### 3. **Test Zoom Levels**
- Zoom out to make events smaller
- Events should switch from bars to diamonds at < 30px width

### 4. **Test Severity**
- Critical events should show red diamond with "!"
- Warning events should show amber diamond with "⚠"
- Normal events should show modality color with first letter

## Files Modified

- `components/timeline/effects/eventRenderers.tsx`: 
  - Created standalone DiamondEventRenderer
  - Removed dependency on BaseActionRenderer
  - Proper positioning and sizing
  - Minimal class usage

- `components/timeline/styles/modality-effects.css`:
  - Removed `!important` overrides
  - Allow inline style control
  - Added z-index for proper layering

## Next Steps

If bars are still visible, the issue might be:
1. **Timeline engine default rendering** - The engine might have its own default bar rendering
2. **Multiple renderers** - Both custom and default renderers might be running
3. **CSS inheritance** - Other CSS rules might be creating bar-like elements

The `getActionRender` prop should completely replace default rendering, so this standalone approach should work.
