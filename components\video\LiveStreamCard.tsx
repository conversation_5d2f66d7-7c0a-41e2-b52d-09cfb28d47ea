"use client"

import { ReactPlayerWrapper } from "./ReactPlayerWrapper"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Volume2, Maximize2, Minimize2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { useVideoTimeline } from "@/components/contexts/VideoTimelineContext"

interface LiveStreamCardProps {
  streamPath: string
  isRecording?: boolean
  title?: string
  operatingRoom?: string
  className?: string
  cardClassName?: string
  contentClassName?: string
  showHeader?: boolean
  maxHeight?: string

  // Video sizing options
  maintainAspectRatio?: boolean
  aspectRatio?: string
  focusMode?: boolean
  onFocusModeToggle?: (focusMode: boolean) => void

  // Player configuration
  controls?: boolean
  muted?: boolean
  volume?: number

  // Legacy props (deprecated - now handled by context)
  //currentTime?: number
  //onTimeUpdate?: (time: number) => void
  //isPlaying?: boolean
 // onPlayStateChange?: (playing: boolean) => void
}

export function LiveStreamCard({
  streamPath,
  isRecording = false,
  title = "Inomed Monitoring System",
  operatingRoom,
  className,
  cardClassName,
  contentClassName,
  showHeader = true,
  maxHeight,
  maintainAspectRatio = true,
  aspectRatio = "16/9",
  focusMode = false,
  onFocusModeToggle,
  controls = false,
  muted = false,
  volume = 1,
  // Legacy props - now handled by context
  //currentTime,
  //onTimeUpdate,
  //isPlaying,
  //onPlayStateChange,
}: LiveStreamCardProps) {
  const videoTimeline = useVideoTimeline()


  return (
    <div className={cn("flex-1", className)}>
      <Card className={cn(cardClassName, focusMode && "border-none shadow-none")}>
        {showHeader && !focusMode && (
          <CardHeader className={cn(focusMode && "hidden")}>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              {title}
              {operatingRoom && (
                <Badge variant="outline">{operatingRoom}</Badge>
              )}
              {isRecording && (
                <Badge variant="destructive" className="animate-pulse">
                  🔴 RECORDING
                </Badge>
              )}
              {videoTimeline.isVideoLoading && (
                <Badge variant="secondary">
                  Loading...
                </Badge>
              )}
              {videoTimeline.videoError && (
                <Badge variant="destructive">
                  Error
                </Badge>
              )}

              {/* Focus Mode Toggle */}
              {onFocusModeToggle && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onFocusModeToggle(!focusMode)}
                  className="ml-auto h-8 w-8 p-0"
                  title="Toggle Focus Mode"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              )}
            </CardTitle>
          </CardHeader>
        )}

        {/* Focus Mode Toggle - Floating when in focus mode */}
        {focusMode && onFocusModeToggle && (
          <div className="absolute top-2 right-2 z-10">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => onFocusModeToggle(false)}
              className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70 text-white border-none"
              title="Exit Focus Mode"
            >
              <Minimize2 className="h-4 w-4" />
            </Button>
          </div>
        )}
        <CardContent className={cn(
          "p-0",
          showHeader && !focusMode && "pt-0",
          focusMode && "p-0",
          contentClassName
        )}>
          <div
            className={cn(
              "relative w-full",
              focusMode && "rounded-none"
            )}
            style={{
              maxHeight: maxHeight || "60vh",
              aspectRatio: maintainAspectRatio ? aspectRatio : undefined,
              height: maintainAspectRatio ? "auto" : maxHeight || "60vh"
            }}
          >
            <ReactPlayerWrapper
              url={streamPath}
              controls={controls}
              muted={muted}
              volume={volume}
              className={cn(
                "w-full h-full overflow-hidden",
                focusMode ? "rounded-none" : "rounded-lg"
              )}
              config={{
                file: {
                  attributes: {
                    crossOrigin: '*',
                  },
                },
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
