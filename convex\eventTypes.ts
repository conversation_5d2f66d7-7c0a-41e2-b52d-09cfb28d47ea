import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const seedEventTypes = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if event types already exist
    const existingEventTypes = await ctx.db.query("eventTypes").collect();
    if (existingEventTypes.length > 0) {
      return { success: false, message: "Event types already exist" };
    }

    // Get all modalities to create event types for them
    const modalities = await ctx.db.query("modalityConfigs").collect();
    if (modalities.length === 0) {
      return { success: false, message: "No modalities found. Please seed modalities first." };
    }

    const eventTypesToCreate = [];

    // Create event types for each modality
    for (const modality of modalities) {
      switch (modality.name) {
        case "ALL":
          eventTypesToCreate.push({
            name: "Manual Event",
            modalityId: modality._id,
            severity: "normal" as const,
            defaultDuration: 15,
            description: "Manually created event",
            isActive: true,
            createdAt: Date.now(),
          });
          break;

        case "EMG":
          eventTypesToCreate.push(
            {
              name: "Burst Activity",
              modalityId: modality._id,
              severity: "warning" as const,
              defaultDuration: 15,
              description: "Increased EMG activity detected",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "Signal Loss",
              modalityId: modality._id,
              severity: "critical" as const,
              defaultDuration: 30,
              description: "Loss of EMG signal",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "Normal Activity",
              modalityId: modality._id,
              severity: "normal" as const,
              defaultDuration: 0,
              description: "Normal EMG activity",
              isActive: true,
              createdAt: Date.now(),
            }
          );
          break;

        case "MEP":
          eventTypesToCreate.push(
            {
              name: "MEP Loss",
              modalityId: modality._id,
              severity: "critical" as const,
              defaultDuration: 30,
              description: "Complete loss of MEP response",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "MEP Recovery",
              modalityId: modality._id,
              severity: "warning" as const,
              defaultDuration: 60,
              description: "Partial recovery of MEP responses",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "Amplitude Decrease",
              modalityId: modality._id,
              severity: "warning" as const,
              defaultDuration: 20,
              description: "Significant decrease in MEP amplitude",
              isActive: true,
              createdAt: Date.now(),
            }
          );
          break;

        case "SSEP":
          eventTypesToCreate.push(
            {
              name: "SSEP Loss",
              modalityId: modality._id,
              severity: "critical" as const,
              defaultDuration: 30,
              description: "Loss of SSEP response",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "Amplitude Decrease",
              modalityId: modality._id,
              severity: "warning" as const,
              defaultDuration: 15,
              description: "Decrease in SSEP amplitude",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "Latency Increase",
              modalityId: modality._id,
              severity: "warning" as const,
              defaultDuration: 10,
              description: "Increase in SSEP latency",
              isActive: true,
              createdAt: Date.now(),
            }
          );
          break;

        case "BAEP":
          eventTypesToCreate.push(
            {
              name: "Wave V Loss",
              modalityId: modality._id,
              severity: "critical" as const,
              defaultDuration: 20,
              description: "Loss of BAEP Wave V",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "Threshold Change",
              modalityId: modality._id,
              severity: "warning" as const,
              defaultDuration: 10,
              description: "Change in auditory threshold",
              isActive: true,
              createdAt: Date.now(),
            }
          );
          break;

        case "VEP":
          eventTypesToCreate.push(
            {
              name: "Visual Response Loss",
              modalityId: modality._id,
              severity: "critical" as const,
              defaultDuration: 25,
              description: "Loss of visual evoked response",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "Pattern Reversal Change",
              modalityId: modality._id,
              severity: "warning" as const,
              defaultDuration: 15,
              description: "Change in pattern reversal VEP",
              isActive: true,
              createdAt: Date.now(),
            }
          );
          break;

        case "AEP":
          eventTypesToCreate.push(
            {
              name: "Auditory Response Loss",
              modalityId: modality._id,
              severity: "critical" as const,
              defaultDuration: 20,
              description: "Loss of auditory evoked response",
              isActive: true,
              createdAt: Date.now(),
            },
            {
              name: "Middle Latency Change",
              modalityId: modality._id,
              severity: "warning" as const,
              defaultDuration: 12,
              description: "Change in middle latency response",
              isActive: true,
              createdAt: Date.now(),
            }
          );
          break;
      }
    }

    // Insert all event types
    const insertedIds = [];
    for (const eventType of eventTypesToCreate) {
      const id = await ctx.db.insert("eventTypes", eventType);
      insertedIds.push(id);
    }

    return { 
      success: true, 
      message: `Successfully seeded ${insertedIds.length} event types`,
      eventTypeIds: insertedIds
    };
  },
});

export const getEventTypesByModality = query({
  args: { modalityId: v.id("modalityConfigs") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("eventTypes")
      .withIndex("by_modality", (q) => q.eq("modalityId", args.modalityId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});

export const getAllEventTypes = query({
  args: {},
  handler: async (ctx) => {
    const eventTypes = await ctx.db
      .query("eventTypes")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();

    // Get modality information for each event type
    const eventTypesWithModalities = await Promise.all(
      eventTypes.map(async (eventType) => {
        const modality = await ctx.db.get(eventType.modalityId);
        return {
          ...eventType,
          modality,
        };
      })
    );

    return eventTypesWithModalities;
  },
});

export const createEventType = mutation({
  args: {
    name: v.string(),
    modalityId: v.id("modalityConfigs"),
    severity: v.union(v.literal("normal"), v.literal("warning"), v.literal("critical")),
    defaultDuration: v.number(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    return await ctx.db.insert("eventTypes", {
      ...args,
      isActive: true,
      createdAt: Date.now(),
    });
  },
});

export const updateEventType = mutation({
  args: {
    eventTypeId: v.id("eventTypes"),
    name: v.optional(v.string()),
    severity: v.optional(v.union(v.literal("normal"), v.literal("warning"), v.literal("critical"))),
    defaultDuration: v.optional(v.number()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const { eventTypeId, ...updates } = args;
    await ctx.db.patch(eventTypeId, updates);
    return { success: true };
  },
});

export const deleteEventType = mutation({
  args: { eventTypeId: v.id("eventTypes") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    // Soft delete by setting isActive to false
    await ctx.db.patch(args.eventTypeId, { isActive: false });
    return { success: true };
  },
});
