import { mutation, query } from "./_generated/server";

export const seedModalityConfigs = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if modalities already exist
    const existingModalities = await ctx.db.query("modalityConfigs").collect();
    if (existingModalities.length > 0) {
      return { success: false, message: "Modalities already exist" };
    }

    const defaultModalities = [
      // Special "ALL" modality for compact timeline view
      {
        name: "ALL",
        displayName: "All Modalities",
        colorCode: "#6b7280", // gray-500
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "EMG",
        displayName: "Electromyography",
        colorCode: "#facc15", // yellow-400
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "MEP",
        displayName: "Motor Evoked Potentials",
        colorCode: "#f87171", // red-400
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "<PERSON><PERSON>",
        displayName: "Somatosensory Evoked Potentials",
        colorCode: "#4ade80", // green-400
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "BAEP",
        displayName: "Brainstem Auditory Evoked Potentials",
        colorCode: "#60a5fa", // blue-400
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "VEP",
        displayName: "Visual Evoked Potentials",
        colorCode: "#a78bfa", // purple-400
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "AEP",
        displayName: "Auditory Evoked Potentials",
        colorCode: "#fb923c", // orange-400
        isActive: true,
        createdAt: Date.now(),
      }
    ];

    const insertedIds = [];
    for (const modality of defaultModalities) {
      const id = await ctx.db.insert("modalityConfigs", modality);
      insertedIds.push(id);
    }

    return { 
      success: true, 
      message: `Successfully seeded ${insertedIds.length} modality configurations`,
      modalityIds: insertedIds
    };
  },
});

export const getActiveModalityConfigs = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("modalityConfigs")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});
