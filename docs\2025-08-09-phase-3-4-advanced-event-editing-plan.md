# Phase 3.4: Advanced Event Editing Interface - Implementation Plan

**Date**: August 9, 2025  
**Status**: Planning Phase  
**Dependencies**: Phase 3.3 (Event Review Interface) - MUST BE COMPLETED FIRST  
**Estimated Duration**: 3 weeks (15-20 hours)  

## Overview

Phase 3.4 creates a comprehensive, medical-grade event editing experience by enhancing and extending Phase 3.3's EventReviewModal into a dual-purpose component serving both review AND advanced editing workflows.

## Dependencies & Integration

### Phase 3.3 Foundation Required
From the existing continuation prompt, Phase 3.3 must deliver:
- ✅ Event Review Modal with video scrubbing
- ✅ Video-Event Synchronization for review playback  
- ✅ Screenshot Integration with image capture
- ✅ Collaborative Annotations with real-time sync
- ✅ Review Workflow with approval process

### Phase 3.4 Enhancement Strategy
Phase 3.4 will **enhance and extend** Phase 3.3's components rather than replace them, ensuring seamless integration and progressive enhancement.

## Implementation Components

### 1. Hybrid Edit/Review Form (4-5 hours)
**Component**: `components/events/EventAdvancedEditForm.tsx`

**Architecture**:
```typescript
interface EventAdvancedEditFormProps {
  eventId: string | null;
  mode: 'edit' | 'review' | 'hybrid'; // Determines available features
  isOpen: boolean;
  onClose: () => void;
  onSave?: (eventId: string) => void;
  onDelete?: (eventId: string) => void;
  onApprove?: (eventId: string) => void; // From Phase 3.3
  onReject?: (eventId: string) => void;  // From Phase 3.3
}
```

**Features**:
- **Tabbed Interface**: Edit | Review | Annotations | Screenshots
- **Form Validation**: Real-time validation with medical compliance
- **Audit Trail**: Track all changes with timestamps and user attribution
- **Collaborative Editing**: Multiple users can edit simultaneously (conflict resolution)
- **Auto-save**: Periodic saves to prevent data loss during long editing sessions

### 2. Integrated Video Player (3-4 hours)
**Component**: `components/events/EventVideoPlayer.tsx`

**Integration Points**:
- **Extends Phase 3.3**: Uses existing video scrubbing from EventReviewModal
- **Enhanced Controls**: Medical-specific playback controls
- **Frame-accurate Positioning**: Sub-second precision for medical documentation

**Medical Controls**:
- **±1 Second Buttons**: Fine adjustment for precise timing
- **±5 Second Buttons**: Coarse adjustment for quick navigation  
- **"Set Start" Button**: Mark event start at current video position
- **"Set End" Button**: Mark event end at current video position
- **Frame Step**: Single frame navigation for critical events
- **Playback Speed**: 0.25x, 0.5x, 1x, 2x for detailed analysis

### 3. Single-Row Timeline Component (2-3 hours)
**Component**: `components/timeline/EventFocusedTimeline.tsx`

**Purpose**: 
- Show only the selected event's modality row
- Enable precise timing adjustments within the edit form
- Provide visual context for event boundaries

**Capabilities**:
- **Event Highlighting**: Selected event prominently displayed
- **Drag Handles**: Resize event start/end times
- **Timeline Centering**: Auto-center on event timespan
- **Zoom Controls**: Focus on event region with appropriate zoom level
- **Context Events**: Show nearby events for reference (dimmed)

### 4. Video Scrubbing Controls (2-3 hours)
**Enhanced from Phase 3.3**: Builds on existing video scrubbing with medical precision

**Medical-Grade Controls**:
- **Time adjustment buttons**: ±1s, ±5s for precise positioning
- **Boundary setting**: Set event start/end at current video position
- **Frame-level precision**: Single frame navigation
- **Playback control**: Variable speed playback (0.25x - 2x)
- **Medical annotations**: Add time markers with notes

**Integration**:
- **Synchronized Playback**: Video cursor matches timeline position
- **Real-time Updates**: Changes reflect immediately in timeline
- **Undo/Redo**: Support for timing adjustments
- **Validation**: Ensure start < end, no negative durations

### 5. Time Format Standardization (1-2 hours)
**Scope**: Convert all time displays from seconds to `(hh:)mm:ss` format

**Components to Update**:
- `EventAdvancedEditForm.tsx` - Form time fields
- `EventFocusedTimeline.tsx` - Timeline labels  
- `EventVideoPlayer.tsx` - Video player display
- `EventSearch.tsx` - Search result times
- `TimelineFilters.tsx` - Time range inputs

**Utility Function**:
```typescript
// utils/timeFormat.ts
export const formatMedicalTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};
```

### 6. Enhanced Editing Capabilities (2-3 hours)
**EventType & Modality Editing**:
- **EventType Dropdown**: Show available types for selected modality
- **Modality Selector**: Auto-filter eventTypes when modality changes
- **Real-time Validation**: EventType/Modality compatibility checks
- **Medical Compliance**: Required fields for medical documentation
- **Conflict Detection**: Warn about overlapping events in same modality

## Integration Architecture

### Phase 3.3 → Phase 3.4 Evolution
```typescript
// Phase 3.3: EventReviewModal (Review-focused)
interface EventReviewModalProps {
  eventId: string;
  mode: 'review';
  // Review-specific props
}

// Phase 3.4: EventAdvancedEditForm (Hybrid)
interface EventAdvancedEditFormProps extends EventReviewModalProps {
  mode: 'edit' | 'review' | 'hybrid';
  // Additional editing props
  onEventTypeChange?: (eventTypeId: string) => void;
  onModalityChange?: (modalityId: string) => void;
  showMedicalControls?: boolean;
}
```

### Component Hierarchy
```
EventAdvancedEditForm (Phase 3.4)
├── EventVideoPlayer (Phase 3.4 - Enhanced from 3.3)
├── EventFocusedTimeline (Phase 3.4 - New)
├── EventAnnotations (Phase 3.3 - Reused)
├── EventScreenshots (Phase 3.3 - Reused)
└── EventReviewWorkflow (Phase 3.3 - Extended)
```

## Success Metrics

### Medical Professional Workflow
- **Editing Time**: 50% reduction in time to edit event details
- **Timing Precision**: Sub-second accuracy for medical documentation
- **User Satisfaction**: 90%+ approval for editing interface usability
- **Error Reduction**: 75% fewer timing errors with visual controls

### Technical Performance
- **Video Sync**: <100ms latency between video and timeline
- **Real-time Updates**: Changes propagate to all users within 500ms
- **Form Validation**: Instant feedback on all field changes
- **Auto-save**: No data loss during editing sessions

## Implementation Timeline

**Week 1**: Foundation
- Day 1-2: EventAdvancedEditForm base structure
- Day 3-4: EventVideoPlayer integration
- Day 5: EventFocusedTimeline component

**Week 2**: Features  
- Day 1-2: Video scrubbing controls
- Day 3: Time format standardization
- Day 4-5: Enhanced editing capabilities

**Week 3**: Integration & Polish
- Day 1-2: Phase 3.3 integration testing
- Day 3-4: Medical workflow validation
- Day 5: Performance optimization and documentation

## Prerequisites

**MUST BE COMPLETED FIRST** (from Phase 3.3 continuation prompt):
1. ✅ EventReviewModal: Base modal with video integration
2. ✅ Video-Event Synchronization: Synchronized playback foundation  
3. ✅ Screenshot Integration: Image capture and storage
4. ✅ Collaborative Annotations: Real-time annotation system
5. ✅ Review Workflow: Approval/rejection process

Phase 3.4 will then EXTEND these components rather than replace them.

---

**Next Steps**: Complete Phase 3.3 implementation, then begin Phase 3.4 development following this plan.
