/**
 * Enhanced mouse interactions for NFM Timeline Editor
 * Provides horizontal scrolling, zoom controls, and enhanced tooltips
 */

import { useEffect, useCallback, useRef, useMemo } from 'react';

export interface TimelineMouseInteractionsConfig {
  enabled?: boolean;
  enableHorizontalScroll?: boolean;
  enableZoomOnScroll?: boolean;
  enableTooltips?: boolean;
  scrollSensitivity?: number;
  zoomSensitivity?: number;
  requireCtrlForZoom?: boolean;
}

export interface TimelineMouseInteractionsHandlers {
  onHorizontalScroll?: (deltaX: number) => void;
  onZoom?: (delta: number, centerX?: number) => void;
  onShowTooltip?: (event: MouseEvent, target: HTMLElement) => void;
  onHideTooltip?: () => void;
}

const DEFAULT_CONFIG: Required<TimelineMouseInteractionsConfig> = {
  enabled: true,
  enableHorizontalScroll: true,
  enableZoomOnScroll: true,
  enableTooltips: true,
  scrollSensitivity: 1,
  zoomSensitivity: 0.1,
  requireCtrlForZoom: true
};

export function useTimelineMouseInteractions(
  timelineRef: React.RefObject<HTMLElement | HTMLDivElement | null>,
  handlers: TimelineMouseInteractionsHandlers,
  config: TimelineMouseInteractionsConfig = {}
) {
  const finalConfig = useMemo(
    () => ({ ...DEFAULT_CONFIG, ...config }),
    [config]
  );
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMouseOverRef = useRef(false);

  // Handle mouse wheel events
  const handleWheel = useCallback((event: WheelEvent) => {
    if (!finalConfig.enabled) return;

    const { deltaX, deltaY, ctrlKey, metaKey } = event;
    const isCtrlOrCmd = ctrlKey || metaKey;

    // Zoom with Ctrl/Cmd + wheel (or just wheel if requireCtrlForZoom is false)
    if (finalConfig.enableZoomOnScroll && (isCtrlOrCmd || !finalConfig.requireCtrlForZoom)) {
      event.preventDefault();
      const zoomDelta = -deltaY * finalConfig.zoomSensitivity;
      const rect = timelineRef.current?.getBoundingClientRect();
      const centerX = rect ? event.clientX - rect.left : undefined;
      handlers.onZoom?.(zoomDelta, centerX);
      return;
    }

    // Horizontal scrolling
    if (finalConfig.enableHorizontalScroll) {
      // Use deltaX for horizontal wheel, or deltaY for vertical wheel (converted to horizontal)
      const scrollDelta = deltaX !== 0 ? deltaX : deltaY;
      if (scrollDelta !== 0) {
        event.preventDefault();
        handlers.onHorizontalScroll?.(scrollDelta * finalConfig.scrollSensitivity);
      }
    }
  }, [finalConfig, handlers, timelineRef]);

  // Handle mouse enter/leave for tooltips
  const handleMouseEnter = useCallback((event: MouseEvent) => {
    if (!finalConfig.enabled || !finalConfig.enableTooltips) return;

    isMouseOverRef.current = true;
    // Clear any existing timeout
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }

    // Show tooltip after a short delay
    tooltipTimeoutRef.current = setTimeout(() => {
      if (isMouseOverRef.current) {
        const target = event.target as HTMLElement;
        const timelineAction = target.closest('[data-action-id]') as HTMLElement;
        if (timelineAction) {
          handlers.onShowTooltip?.(event, timelineAction);
        }
      }
    }, 500); // 500ms delay
  }, [finalConfig, handlers]);

  const handleMouseLeave = useCallback(() => {
    if (!finalConfig.enabled || !finalConfig.enableTooltips) return;

    isMouseOverRef.current = false;
    
    // Clear timeout and hide tooltip
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
      tooltipTimeoutRef.current = null;
    }
    
    handlers.onHideTooltip?.();
  }, [finalConfig, handlers]);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!finalConfig.enabled || !finalConfig.enableTooltips) return;

    const target = event.target as HTMLElement;
    const timelineAction = target.closest('[data-action-id]') as HTMLElement;
    
    // If we moved to a different action, reset tooltip
    if (timelineAction) {
      // Clear existing timeout
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
      
      // Start new timeout for this action
      tooltipTimeoutRef.current = setTimeout(() => {
        if (isMouseOverRef.current) {
          handlers.onShowTooltip?.(event, timelineAction);
        }
      }, 300); // Shorter delay for move events
    } else {
      // Not over an action, hide tooltip
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
        tooltipTimeoutRef.current = null;
      }
      handlers.onHideTooltip?.();
    }
  }, [finalConfig, handlers]);

  // Touch support for mobile devices
  const handleTouchStart = useCallback((event: TouchEvent) => {
    if (!finalConfig.enabled) return;
    
    // Store initial touch position for gesture detection
    const touch = event.touches[0];
    if (touch && timelineRef.current) {
      const rect = timelineRef.current.getBoundingClientRect();
      const startX = touch.clientX - rect.left;
      
      // Store in a data attribute for touch move handling
      timelineRef.current.dataset.touchStartX = startX.toString();
    }
  }, [finalConfig, timelineRef]);

  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (!finalConfig.enabled || !finalConfig.enableHorizontalScroll) return;
    
    const touch = event.touches[0];
    if (touch && timelineRef.current) {
      const startX = parseFloat(timelineRef.current.dataset.touchStartX || '0');
      const rect = timelineRef.current.getBoundingClientRect();
      const currentX = touch.clientX - rect.left;
      const deltaX = startX - currentX;
      
      if (Math.abs(deltaX) > 5) { // Minimum movement threshold
        event.preventDefault();
        handlers.onHorizontalScroll?.(deltaX * finalConfig.scrollSensitivity);
        
        // Update start position for continuous scrolling
        timelineRef.current.dataset.touchStartX = currentX.toString();
      }
    }
  }, [finalConfig, handlers, timelineRef]);

  const handleTouchEnd = useCallback(() => {
    if (timelineRef.current) {
      delete timelineRef.current.dataset.touchStartX;
    }
  }, [timelineRef]);

  // Pinch-to-zoom support
  const handleTouchGesture = useCallback((event: TouchEvent) => {
    if (!finalConfig.enabled || !finalConfig.enableZoomOnScroll) return;
    
    if (event.touches.length === 2) {
      event.preventDefault();
      
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      
      // Calculate distance between touches
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      // Store initial distance
      if (!timelineRef.current?.dataset.initialPinchDistance) {
        timelineRef.current!.dataset.initialPinchDistance = distance.toString();
        return;
      }
      
      const initialDistance = parseFloat(timelineRef.current.dataset.initialPinchDistance);
      const scale = distance / initialDistance;
      const zoomDelta = (scale - 1) * finalConfig.zoomSensitivity;
      
      // Calculate center point
      const centerX = (touch1.clientX + touch2.clientX) / 2;
      const rect = timelineRef.current.getBoundingClientRect();
      const relativeCenterX = centerX - rect.left;
      
      handlers.onZoom?.(zoomDelta, relativeCenterX);
      
      // Update initial distance for continuous zooming
      timelineRef.current.dataset.initialPinchDistance = distance.toString();
    }
  }, [finalConfig, handlers, timelineRef]);

  // Set up event listeners
  useEffect(() => {
    const element = timelineRef.current;
    if (!element || !finalConfig.enabled) return;

    // Mouse events
    element.addEventListener('wheel', handleWheel as EventListener, { passive: false });
    element.addEventListener('mouseenter', handleMouseEnter as EventListener);
    element.addEventListener('mouseleave', handleMouseLeave as EventListener);
    element.addEventListener('mousemove', handleMouseMove as EventListener);

    // Touch events
    element.addEventListener('touchstart', handleTouchStart as EventListener, { passive: false });
    element.addEventListener('touchmove', handleTouchMove as EventListener, { passive: false });
    element.addEventListener('touchend', handleTouchEnd as EventListener);
    
    // Pinch-to-zoom
    element.addEventListener('touchmove', handleTouchGesture as EventListener, { passive: false });

    return () => {
      element.removeEventListener('wheel', handleWheel as EventListener);
      element.removeEventListener('mouseenter', handleMouseEnter as EventListener);
      element.removeEventListener('mouseleave', handleMouseLeave as EventListener);
      element.removeEventListener('mousemove', handleMouseMove as EventListener);
      element.removeEventListener('touchstart', handleTouchStart as EventListener);
      element.removeEventListener('touchmove', handleTouchMove as EventListener);
      element.removeEventListener('touchend', handleTouchEnd as EventListener);
      element.removeEventListener('touchmove', handleTouchGesture as EventListener);
      
      // Clean up any pending timeouts
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
    };
  }, [
    finalConfig.enabled,
    handleWheel,
    handleMouseEnter,
    handleMouseLeave,
    handleMouseMove,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleTouchGesture,
    timelineRef
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
    };
  }, []);
}


