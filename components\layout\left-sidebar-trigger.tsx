"use client"

import { PanelRight, PanelRightClose } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface RightSidebarTriggerProps {
  onToggleCollapse: () => void
  isCollapsed: boolean
}

export function RightSidebarTrigger({ onToggleCollapse, isCollapsed }: RightSidebarTriggerProps) {
  return (
    <Button
      variant="ghost"
      size="icon"
      className="h-7 w-7"
      onClick={onToggleCollapse}
      title={isCollapsed ? "Expand Right Sidebar" : "Collapse Right Sidebar"}
    >
      {isCollapsed ? <PanelRight /> : <PanelRightClose />}
      <span className="sr-only">Toggle Right Sidebar Collapse</span>
    </Button>
  )
}
