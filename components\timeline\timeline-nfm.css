/**
 * NFM Timeline Editor Styles
 * Custom styles for the react-timeline-editor integration
 */

/* Base timeline action styles */
.timeline-action {
  /* Remove transition during resize to prevent visual artifacts */
  transition: box-shadow 0.2s ease-in-out, z-index 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  /* Ensure consistent positioning */
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: auto; /* Reset will-change to prevent unnecessary compositing */
}

/* Disable all transitions during resize operations to prevent visual shifting */
.timeline-action.timeline-action-resizing,
.timeline-action.timeline-action-dragging {
  transition: none !important;
}

/* Ensure other actions on the same row maintain their position during resize */
.timeline-row.timeline-row-resizing .timeline-action:not(.timeline-action-resizing) {
  transition: none !important;
  position: absolute !important;
}

.timeline-action:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.timeline-action.timeline-action-selected {
  ring: 2px solid #3b82f6;
  ring-offset: 1px;
  z-index: 20;
}

/* Active state for playing actions */
.timeline-action-active {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0%, 100% {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
  }
}

/* Progress indicator */
.timeline-action-progress::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: var(--progress, 0%);
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  transition: width 0.1s ease-out;
  pointer-events: none;
}

/* Severity-specific styles */
.timeline-action-critical {
  animation: critical-pulse 1s infinite;
  box-shadow: 0 0 8px rgba(220, 38, 38, 0.6);
}

@keyframes critical-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: var(--pulse-opacity, 0.8);
    transform: scale(1.02);
  }
}

.timeline-action-alarm {
  animation: alarm-flash 0.5s infinite alternate;
}

@keyframes alarm-flash {
  0% {
    background-color: #ef4444;
  }
  100% {
    background-color: #dc2626;
  }
}

.timeline-action-warning {
  position: relative;
}

.timeline-action-warning::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-top: 8px solid #fbbf24;
}

/* Dynamic modality-specific styles using CSS custom properties */
.timeline-editor-action[class*="action-effect-modality-"] {
  background: var(--modality-color, #6b7280);
  border: 1px solid var(--modality-color-dark, #4b5563);
  border-radius: 4px;
  color: white;
  font-size: 11px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

/* Gradient overlay for better visual appeal */
.timeline-editor-action[class*="action-effect-modality-"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  pointer-events: none;
}

/* Diamond shape additional styling - positioning is handled in modality-effects.css */
.timeline-editor-action.timeline-action-diamond {
  border-radius: 0;
  min-width: 20px !important;
}

.timeline-editor-action.timeline-action-diamond::before {
  border-radius: 0;
}

/* Additional diamond content styling */
.timeline-editor-action.timeline-action-diamond .timeline-action-content {
  font-size: 8px; /* Slightly smaller for better fit */
}

/* Hide resize handles for diamond shapes */
.timeline-editor-action.timeline-action-diamond .timeline-editor-action-left-stretch,
.timeline-editor-action.timeline-action-diamond .timeline-editor-action-right-stretch {
  display: none;
}

/* Event type specific styling */
.timeline-action-event-critical {
  animation: critical-pulse 1s infinite;
  box-shadow: 0 0 8px rgba(220, 38, 38, 0.6);
}

.timeline-action-event-alarm {
  animation: alarm-flash 0.5s infinite alternate;
}

.timeline-action-event-warning::after {
  content: '⚠️';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 10px;
  line-height: 1;
}

.timeline-action-event-info::after {
  content: 'ℹ️';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 10px;
  line-height: 1;
}

/* Icon support for different event types */
.timeline-action-icon {
  position: absolute;
  top: 50%;
  left: 4px;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  z-index: 1;
}

/* Text content positioning when icon is present */
.timeline-action-with-icon .timeline-action-text {
  margin-left: 18px;
}

/* Expandable icon system for future custom icons */
.timeline-action-icon-note::before { content: '📝'; }
.timeline-action-icon-surgery::before { content: '🔪'; }
.timeline-action-icon-critical::before { content: '🚨'; }
.timeline-action-icon-warning::before { content: '⚠️'; }
.timeline-action-icon-info::before { content: 'ℹ️'; }
.timeline-action-icon-success::before { content: '✅'; }
.timeline-action-icon-error::before { content: '❌'; }
.timeline-action-icon-pause::before { content: '⏸️'; }
.timeline-action-icon-play::before { content: '▶️'; }
.timeline-action-icon-stop::before { content: '⏹️'; }

/* Modality-specific icons */
.timeline-action-icon-emg::before { content: '⚡'; }
.timeline-action-icon-eeg::before { content: '🧠'; }
.timeline-action-icon-ecg::before { content: '💓'; }
.timeline-action-icon-video::before { content: '📹'; }
.timeline-action-icon-audio::before { content: '🔊'; }
.timeline-action-icon-mep::before { content: '⚡'; }
.timeline-action-icon-ssep::before { content: '🔌'; }

/* Compact action styles */
.timeline-action-compact {
  min-width: 8px !important;
  border-radius: 50%;
  padding: 0;
}

.timeline-action-compact:hover {
  transform: scale(1.5);
}

/* Timeline cursor styles */
.timeline-cursor {
  transition: all 0.2s ease-in-out;
}

.timeline-cursor-playing {
  filter: drop-shadow(0 0 4px rgba(239, 68, 68, 0.8));
}

/* Row styles */
.timeline-row {
  transition: background-color 0.2s ease-in-out;
}

.timeline-row:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

.timeline-row-selected {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #3b82f6;
}

/* Grid styles */
.timeline-grid {
  opacity: 0.3;
  transition: opacity 0.2s ease-in-out;
}

.timeline-editor-nfm:hover .timeline-grid {
  opacity: 0.5;
}

/* Scale area styles */
.timeline-scale {
  background: linear-gradient(to bottom, #f9fafb, #f3f4f6);
  border-bottom: 1px solid #e5e7eb;
}

.timeline-scale-dark {
  background: linear-gradient(to bottom, #1f2937, #111827);
  border-bottom: 1px solid #374151;
}

/* Snap indicators */
.timeline-snap-line {
  position: absolute;
  background-color: #3b82f6;
  opacity: 0.7;
  z-index: 100;
  pointer-events: none;
  transition: opacity 0.1s ease-out;
}

.timeline-snap-line-vertical {
  width: 1px;
  height: 100%;
}

.timeline-snap-line-horizontal {
  height: 1px;
  width: 100%;
}

/* Selection box */
.timeline-selection-box {
  position: absolute;
  border: 2px dashed #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  pointer-events: none;
  z-index: 50;
}

/* Drag preview */
.timeline-drag-preview {
  opacity: 0.7;
  transform: rotate(2deg);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

/* Resize handles */
.timeline-resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  cursor: ew-resize;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.timeline-action:hover .timeline-resize-handle {
  opacity: 1;
}

.timeline-resize-handle-left {
  left: 0;
  border-radius: 2px 0 0 2px;
}

.timeline-resize-handle-right {
  right: 0;
  border-radius: 0 2px 2px 0;
}

/* Context menu */
.timeline-context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 160px;
  padding: 4px 0;
}

.timeline-context-menu-dark {
  background: #1f2937;
  border-color: #374151;
  color: white;
}

.timeline-context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.1s ease-out;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.timeline-context-menu-item:hover {
  background-color: #f3f4f6;
}

.timeline-context-menu-dark .timeline-context-menu-item:hover {
  background-color: #374151;
}

.timeline-context-menu-separator {
  height: 1px;
  background-color: #e5e7eb;
  margin: 4px 0;
}

.timeline-context-menu-dark .timeline-context-menu-separator {
  background-color: #374151;
}

/* Loading states */
.timeline-loading {
  position: relative;
  overflow: hidden;
}

.timeline-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Error states */
.timeline-error {
  border-color: #ef4444 !important;
  background-color: rgba(239, 68, 68, 0.1);
}

.timeline-error-message {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-top: none;
  padding: 8px 12px;
  font-size: 12px;
  color: #dc2626;
  z-index: 10;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .timeline-action {
    min-height: 28px;
  }
  
  .timeline-action-compact {
    min-width: 6px !important;
  }
  
  .timeline-resize-handle {
    width: 6px;
  }
}

/* Timeline row headers */
.timeline-row-header {
  position: relative;
  z-index: 10;
}

.timeline-row-header:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

.timeline-row-headers {
  background: white;
  border-right: 1px solid #e5e7eb;
}

.dark .timeline-row-headers {
  background: #1f2937;
  border-right-color: #374151;
}

/* Ensure timeline content aligns with headers */
.timeline-editor-nfm .timeline-editor {
  border-radius: 0 8px 8px 0; /* Only round right corners */
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .timeline-editor-nfm {
    --timeline-bg: #1f2937;
    --timeline-border: #374151;
    --timeline-text: #f9fafb;
  }

  .timeline-action {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .timeline-context-menu {
    background: #1f2937;
    border-color: #374151;
    color: white;
  }
}
