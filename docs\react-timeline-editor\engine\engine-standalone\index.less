@import "~antd/dist/antd.css";

.timeline-editor-engine {

  .player-panel {
    width: 100%;
    max-width: 800px;
    height: 300px;
    position: relative;

    .lottie-ani {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }
  }

  .timeline-player {
    height: 32px;
    width: 100%;
    max-width: 800px;
    padding: 0 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #3a3a3a;
    color: #ddd;

    .play-control {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      display: flex;
      background-color: #666;
      justify-content: center;
      align-items: center;
    }
    .play-time {
      flex: 1 1 auto;
      display: flex;
      flex-direction: row;

      &-current, &-duration {
        width: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 0 0 auto;
      }

      &-slider {
        flex: 1 1 auto;
      }
    }
  }
}


