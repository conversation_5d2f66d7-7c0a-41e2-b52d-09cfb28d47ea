@import "~antd/dist/antd.css";


@color-audio: #cd9541;
@color-text-basic: #fff;
@fontsize-small: 10px;
@border-radius: 4px;


.timeline-editor-example2 {

  .timeline-editor {
    width: 100%;
    max-width: 800px;
    height: 300px;
  
    &-action {
      height: 28px !important;
      top: 50%;
      transform: translateY(-50%);
    }  

    &-action {

      &-effect-effect0 {
        cursor: pointer;
        background-color: @color-audio;
        background-image: url("/assets/soundWave.png");
        background-position: bottom;
        background-repeat: repeat-x;
        .effect0 {
          width: 100%;
          height: 100%;
          font-size: @fontsize-small;
          color: @color-text-basic;
          border-radius: @border-radius;
          display: flex;
          flex-direction: row;
          align-items: center;
    
          &-text {
            margin-left: 4px;
            flex: 1 1 auto;
            text-align: center;
            display: flex;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
          }
        
        }


        .timeline-editor-action-left-stretch,
        .timeline-editor-action-right-stretch {
          overflow: inherit;

          &::after {
            width: 18px;
            height: 18px;
            transform: rotate(
              45deg
            ) scale(.8);
            background: #aabbcc;
            border: none;
          }
        }

        .timeline-editor-action-left-stretch::after {
          left: -9px;
        }
    
        .timeline-editor-action-right-stretch::after {
          right: -9px;
        }
      }
      
      .effect1 {
        width: 25px;
        height: 28px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);

        & img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
