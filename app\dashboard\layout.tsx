"use client";

import { ProjectProvider } from "@/components/contexts/ProjectContext";
import { MainLayout } from "@/components/layout/main-layout";
import { api } from "@/convex/_generated/api";
import { useQuery } from "convex/react";
import { usePathname } from "next/navigation";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const activeCount = useQuery(api.projects.getActiveProjectsCount);
  const pathname = usePathname();

  // Determine page context based on pathname
  const getPageContext = () => {
    if (pathname.includes('/live-monitoring')) {
      return {
        pageTitle: 'Live Monitoring',
        pageSubtitle: 'Real-time neuroFysiology monitoring session',
        isLive: true
      };
    }
    if (pathname.includes('/timeline-review')) {
      return {
        pageTitle: 'Timeline Review',
        pageSubtitle: 'Review and analyze monitoring sessions'
      };
    }
    if (pathname.includes('/reports')) {
      return {
        pageTitle: 'Reports',
        pageSubtitle: 'Generate and view monitoring reports'
      };
    }
    if (pathname.includes('/settings')) {
      return {
        pageTitle: 'Settings',
        pageSubtitle: 'Configure system and user preferences'
      };
    }
    return {};
  };

  const pageContext = getPageContext();

  return (
    <ProjectProvider initialApplicationState="live-monitoring">
       <MainLayout
            activeSessionsCount={activeCount || 0}
            {...pageContext}
          >
            {children}
          </MainLayout>
    </ProjectProvider>
  );
}
