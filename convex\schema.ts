import { defineSchema } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";
import { Table } from "convex-helpers/server";
import { literals, deprecated } from "convex-helpers/validators";

// Define table schemas using the Table pattern for type safety
export const Users = Table("users", {
  email: v.string(),
  name: v.string(), 
  //Split ROLES AND PERMISSIONS into two variables
  role: v.union(
    v.literal("default"),
    v.literal("surgeon"),
    v.literal("anesthesiologist"), 
    v.literal("neurophysiologist"),
    v.literal("admin"),
    v.literal("technician")
  ),
  specialization: v.optional(v.string()),
  license: v.optional(v.string()),
  credentials: v.optional(v.array(v.string())),
  department: v.optional(v.string()),
  phone: v.optional(v.string()),
  preferences: v.optional(v.object({
    defaultModalities: v.array(v.string()),
    notificationSettings: v.object({
      email: v.boolean(),
      inApp: v.boolean(),
      criticalEvents: v.boolean(),
    }),
    uiPreferences: v.object({
      theme: v.union(v.literal("light"), v.literal("dark")),
      timelineView: v.union(v.literal("compact"), v.literal("expanded")),
      defaultTimeScale: v.number(),
    }),
  })),
  isActive: v.optional(v.boolean()),
  lastLogin: v.optional(v.number()),
  createdAt: v.optional(v.number()),
});

export const Patients = Table("patients", {
  firstName: v.string(),
  lastName: v.string(),
  address: v.optional(v.string()),
  contactPhone: v.optional(v.string()),
  medicalRecordNumber: v.string(),
  dateOfBirth: v.number(),
  gender: v.union(v.literal("male"), v.literal("female"), v.literal("other")),
  emergencyContact: v.optional(v.object({
    name: v.string(),
    relationship: v.string(),
    phone: v.string(),
  })),
  allergies: v.optional(v.string()),
  medications: v.array(v.object({
    name: v.string(),
    dosage: v.string(),
    frequency: v.string(),
  })),
  medicalHistory: v.array(v.string()),
  diagnosis: v.optional(v.string()),
  notes: v.optional(v.string()),
  createdBy: v.id("users"),
  createdAt: v.optional(v.number()),
  updatedAt: v.optional(v.number()),
});

export const Projects = Table("projects", {
  projectCode: v.string(),
  patientId: v.id("patients"),
  surgeryType: v.string(),
  surgerySubtype: v.optional(v.string()),
  operatingRoom: v.string(),
  status: v.union(
    v.literal("scheduled"),
    v.literal("pre-op"),
    v.literal("in-progress"),
    v.literal("post-op"),
    v.literal("completed"),
    v.literal("cancelled")
  ),
  teamMembers: v.object({
    primarySurgeon: v.id("users"),
    assistingSurgeons: v.array(v.id("users")),
    anesthesiologist: v.id("users"),
    neurophysiologist: v.id("users"),
    nurses: v.array(v.id("users")),
    technicians: v.array(v.id("users")),
  }),
  scheduledStart: v.number(),
  scheduledDuration: v.number(),
  actualStart: v.optional(v.number()),
  actualEnd: v.optional(v.number()),
  enabledModalities: v.array(v.id("modalityConfigs")),
  visibleModalities: v.optional(v.array(v.id("modalityConfigs"))), // User-selected visible modalities
  streamingSources: v.array(v.object({
    name: v.string(),
    type: literals("rtsp","screen","camera"),
    url: v.string(),
    isActive: v.boolean(),
  })),
  preOpNotes: v.optional(v.string()),
  postOpNotes: v.optional(v.string()),
  complications: v.array(v.string()),
  createdBy: v.id("users"),
  createdAt: v.number(),
  updatedAt: v.number(),
});



export const SurgeryTypes = Table("surgeryTypes", {
  name: v.string(),
  category: v.string(),
  defaultModalities: v.array(v.id("modalityConfigs")),
  defaultEventButtons: v.array(v.object({
    modalityId: v.id("modalityConfigs"),
    eventType: deprecated, // Backward compatibility - can be removed later
    eventTypeId: v.optional(v.id("eventTypes")),
    label: v.string(),
    severity: literals("normal", "warning","critical"),
  })),
  estimatedDuration: v.number(),
  isActive: v.boolean(),
});

export const StreamConfigs = Table("streamConfigs", {
  pathName: v.string(),
  sourceUrl: v.string(),
  streamType: v.optional(v.union(
    v.literal("inomed"),
    v.literal("microscope"),
    v.literal("camera"),
    v.literal("screen"),
    v.literal("external"),
    v.literal("local")
  )),
  description: v.optional(v.string()),
  isActive: v.boolean(),
  isEnabled: v.boolean(),
  isLive: v.optional(v.boolean()),
  isInMediaMTX: v.optional(v.boolean()),
  viewers: v.optional(v.number()),
  originatedFromMediaMTX: v.optional(v.boolean()),
  lastSyncStatus: v.optional(v.union(
    v.literal("synced"),
    v.literal("needs_sync"),
    v.literal("conflict")
  )),
  createdAt: v.number(),
  updatedAt: v.number(),
});

export const ModalityConfigs = Table("modalityConfigs", {
  name: v.string(),
  displayName: v.string(),
  colorCode: v.string(),
  isActive: v.boolean(),
  createdAt: v.number(),
});

export const EventTypes = Table("eventTypes", {
  name: v.string(),
  modalityId: v.id("modalityConfigs"),
  severity: v.union(
    v.literal("normal"),
    v.literal("warning"),
    v.literal("critical")
  ),
  defaultDuration: v.number(),
  description: v.optional(v.string()),
  isActive: v.boolean(),
  createdAt: v.number(),
});

export const MonitoringEvents = Table("monitoringEvents", {
  projectId: v.id("projects"),
  sessionId: v.id("streamSessions"),
  timestamp: v.optional(v.number()), // Keep for backward compatibility - equals startTime
  startTime: v.number(), // Event start time in seconds
  endTime: v.optional(v.number()), // Event end time - optional for point events
  modalityId: v.id("modalityConfigs"),
  eventTypeId: v.id("eventTypes"),
  // Keep eventType string for backward compatibility during migration
  //eventType: v.optional(v.string()),
  /*severity: deprecated, /*v.optional(v.union( // Optional for backward compatibility    
    v.literal("normal"),
    v.literal("warning"),
    v.literal("critical")
  )),*/
  title: v.string(),
  description: v.string(),
  location: v.optional(v.string()),
  screenshots: v.array(v.id("_storage")),
  videoClip: v.optional(v.object({
    startTime: v.number(),
    endTime: v.number(),
    storageId: v.id("_storage"),
  })),
  reviewerId: v.optional(v.id("users")),
  reviewStatus: v.union(
    v.literal("unreviewed"),
    v.literal("under-review"),
    v.literal("reviewed"),
    v.literal("flagged")
  ),
  reviewNotes: v.optional(v.string()),
  createdBy: v.id("users"),
  createdAt: v.number(),
  updatedAt: v.number(),
});

export const ClinicalExams = Table("clinicalExams", {
  projectId: v.id("projects"),
  type: v.union(v.literal("pre-op"), v.literal("post-op")),
  cranialNerves: v.array(v.object({
    nerve: v.string(),
    status: v.union(
      v.literal("normal"),
      v.literal("abnormal"),
      v.literal("not-tested")
    ),
    notes: v.optional(v.string()),
  })),
  motorFunction: v.array(v.object({
    muscle: v.string(),
    side: v.union(v.literal("left"), v.literal("right")),
    strength: v.number(),
    notes: v.optional(v.string()),
  })),
  sensoryFunction: v.array(v.object({
    dermatome: v.string(),
    side: v.union(v.literal("left"), v.literal("right")),
    sensation: v.union(
      v.literal("normal"),
      v.literal("decreased"),
      v.literal("absent"),
      v.literal("hyperesthetic")
    ),
    notes: v.optional(v.string()),
  })),
  reflexes: v.array(v.object({
    reflex: v.string(),
    side: v.union(v.literal("left"), v.literal("right")),
    response: v.union(
      v.literal("absent"),
      v.literal("diminished"),
      v.literal("normal"),
      v.literal("brisk"),
      v.literal("hyperactive")
    ),
  })),
  overallStatus: v.union(
    v.literal("normal"),
    v.literal("abnormal"),
    v.literal("critical")
  ),
  summaryNotes: v.string(),
  recommendations: v.array(v.string()),
  examiner: v.id("users"),
  timestamp: v.number(),
});

export const StreamSessions = Table("streamSessions", {
  projectId: v.id("projects"),
 // sessionId: v.string(),
  status: v.union(
    v.literal("starting"),
    v.literal("live"),
    v.literal("paused"),
    v.literal("stopped"),
    v.literal("error")
  ),
  streamSources: v.array(v.object({
    name: v.string(),
    url: v.string(),
    quality: v.string(),
    isActive: v.boolean(),
  })),
  startTime: v.number(),
  endTime: v.optional(v.number()),
  recordingPath: v.optional(v.string()),
  viewers: v.array(v.id("users")),
  eventCount: v.number(),
  lastEventTime: v.optional(v.number()),
});

export const SystemConfig = Table("systemConfig", {
  key: v.string(),
  value: v.any(),
  description: v.optional(v.string()),
  updatedBy: v.id("users"),
  updatedAt: v.number(),
});

// Export field validators for reuse
export const { role: userRoleValidator } = Users.withoutSystemFields;
export const { status: projectStatusValidator } = Projects.withoutSystemFields;
export const { gender: patientGenderValidator } = Patients.withoutSystemFields;
export const { severity: eventSeverityValidator } = MonitoringEvents.withoutSystemFields;
export const { streamType: streamTypeValidator } = StreamConfigs.withoutSystemFields;
//export const { modality: streamTypeValidator } = ModalityConfigs.withSystemFields;

// Define schema with proper indexes
export default defineSchema({
  ...authTables,
  
  // Users and Authentication
  users: Users.table
    .index("by_email", ["email"])
    .index("by_role", ["role"])
    .index("by_active", ["isActive"]),

  // Projects (Surgery Sessions)
  projects: Projects.table
    .index("by_status", ["status"])
    .index("by_date", ["scheduledStart"])
    .index("by_patient", ["patientId"])
    .index("by_room", ["operatingRoom"])
    .index("by_surgeon", ["teamMembers.primarySurgeon"]),

  // Patients
  patients: Patients.table
    .index("by_mrn", ["medicalRecordNumber"])
    .index("by_name", ["lastName", "firstName"]),

  // Modality Configurations
  modalityConfigs: ModalityConfigs.table
    .index("by_active", ["isActive"]),

  // Event Types
  eventTypes: EventTypes.table
    .index("by_modality", ["modalityId"])
    .index("by_active", ["isActive"])
    .index("by_severity", ["severity"]),

  // Surgery Type Templates
  surgeryTypes: SurgeryTypes.table
    .index("by_category", ["category"])
    .index("by_active", ["isActive"]),

  // Stream Configurations
  streamConfigs: StreamConfigs.table
    .index("by_path", ["pathName"])
    .index("by_type", ["streamType"])
    .index("by_active", ["isActive"])
    .index("by_enabled", ["isEnabled"])
    .index("by_origin", ["originatedFromMediaMTX"]),

  // Monitoring Events
  monitoringEvents: MonitoringEvents.table
    .index("by_project", ["projectId"])
    .index("by_session", ["sessionId"])
    .index("by_timestamp", ["timestamp"])
    .index("by_modality", ["modalityId"])
    .index("by_event_type", ["eventTypeId"])
    .index("by_review_status", ["reviewStatus"]),
    //.index("by_project_modality", ["projectId", "modalityId"]),

  // Clinical Examinations
  clinicalExams: ClinicalExams.table
    .index("by_project", ["projectId"])
    .index("by_type", ["type"]),

  // Stream Sessions (for tracking live sessions)
  streamSessions: StreamSessions.table
    .index("by_project", ["projectId"])
    .index("by_status", ["status"]),
   // .index("by_session", ["sessionId"]),

  // System Configuration
  systemConfig: SystemConfig.table
    .index("by_key", ["key"]),
});
