/**
 * Dynamic CSS injection for timeline modality styling
 */

import { Doc } from '@/convex/_generated/dataModel';

/**
 * Adjusts color brightness for borders and hover effects
 */
function adjustColorBrightness(color: string, amount: number): string {
  // Remove # if present
  const hex = color.replace('#', '');
  
  // Parse RGB values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  // Adjust brightness
  const newR = Math.max(0, Math.min(255, r + amount));
  const newG = Math.max(0, Math.min(255, g + amount));
  const newB = Math.max(0, Math.min(255, b + amount));
  
  // Convert back to hex
  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}

/**
 * Creates CSS rules for modality-specific styling
 */
function createModalityCSS(modalities: Doc<"modalityConfigs">[]): string {
  const cssRules: string[] = [];
  
  modalities.forEach(modality => {
    if (modality.isActive && modality.colorCode) {
      const effectId = `modality-${modality._id}`;
      const color = modality.colorCode;
      const darkColor = adjustColorBrightness(color, -30);
      const lightColor = adjustColorBrightness(color, 20);
      
      // Main action styling
      cssRules.push(`
        .timeline-editor-action-effect-${effectId} {
          background-color: ${color} !important;
          border-color: ${darkColor} !important;
          color: white !important;
          --modality-color: ${color};
          --modality-color-dark: ${darkColor};
          --modality-color-light: ${lightColor};
        }
      `);
      
      // Hover state
      cssRules.push(`
        .timeline-editor-action-effect-${effectId}:hover {
          background-color: ${lightColor} !important;
          border-color: ${color} !important;
        }
      `);
      
      // Selected state
      cssRules.push(`
        .timeline-editor-action-effect-${effectId}.action-selected {
          box-shadow: 0 0 0 2px ${color}, 0 0 0 4px rgba(59, 130, 246, 0.3) !important;
        }
      `);
      
      // Diamond shape styling
      cssRules.push(`
        .timeline-action-diamond.timeline-editor-action-effect-${effectId} {
          background-color: ${color} !important;
          border-color: ${darkColor} !important;
        }
      `);
      
      // Resize handles
      cssRules.push(`
        .timeline-editor-action-effect-${effectId} .timeline-editor-action-left-stretch,
        .timeline-editor-action-effect-${effectId} .timeline-editor-action-right-stretch {
          background: linear-gradient(to right, rgba(255,255,255,0.4), transparent);
        }
      `);
    }
  });
  
  return cssRules.join('\n');
}

/**
 * Injects or updates dynamic CSS for modality styling
 */
export function injectModalityStyles(modalities: Doc<"modalityConfigs">[]): void {
  const styleId = 'nfm-timeline-modality-styles';
  
  // Remove existing style element
  const existingStyle = document.getElementById(styleId);
  if (existingStyle) {
    existingStyle.remove();
  }
  
  // Create new style element
  const styleElement = document.createElement('style');
  styleElement.id = styleId;
  styleElement.type = 'text/css';
  
  // Generate CSS
  const css = createModalityCSS(modalities);
  
  // Add CSS to style element
  if (styleElement.styleSheet) {
    // IE support
    (styleElement.styleSheet as any).cssText = css;
  } else {
    styleElement.appendChild(document.createTextNode(css));
  }
  
  // Append to head
  document.head.appendChild(styleElement);
}

/**
 * Removes dynamic modality styles
 */
export function removeModalityStyles(): void {
  const styleId = 'nfm-timeline-modality-styles';
  const existingStyle = document.getElementById(styleId);
  if (existingStyle) {
    existingStyle.remove();
  }
}

/**
 * Updates modality styles when modalities change
 */
export function updateModalityStyles(modalities: Doc<"modalityConfigs">[]): void {
  // Only inject styles if we have active modalities with colors
  const activeModalitiesWithColors = modalities.filter(m => m.isActive && m.colorCode);
  
  if (activeModalitiesWithColors.length > 0) {
    injectModalityStyles(modalities);
  } else {
    removeModalityStyles();
  }
}
