import React, { FC } from 'react';
import { TimelineRow } from '../../interface/action';
import { CommonProp } from '../../interface/common_prop';
import { prefix } from '../../utils/deal_class_prefix';
import { parserPixelToTime } from '../../utils/deal_data';
import { DragLineData } from './drag_lines';
import { EditAction } from './edit_action';
import './edit_row.css';

export type EditRowProps = CommonProp & {
  areaRef: React.RefObject<HTMLDivElement>;
  rowData?: TimelineRow;
  style?: React.CSSProperties;
  dragLineData: DragLineData;
  setEditorData: (params: TimelineRow[]) => void;
  /** Scroll distance to the left */
  scrollLeft: number;
  /** Set scroll left */
  deltaScrollLeft?: (scrollLeft: number) => void;
  /** Disable drag operations */
  disableDrag?: boolean;
};

export const EditRow: FC<EditRowProps> = (props) => {
  const {
    rowData,
    style = {}, 
    onClickRow,
    onDoubleClickRow,
    onContextMenuRow,
    areaRef,
    scrollLeft,
    startLeft,
    scale,
    scaleWidth,
  } = props;

  const classNames = ['edit-row'];
  if (rowData?.selected) classNames.push('edit-row-selected');

  const handleTime = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    const rect = areaRef.current?.getBoundingClientRect();
    const position = e.clientX - rect.x;
    const left = position + scrollLeft;
    const time = parserPixelToTime(left, { startLeft: startLeft!, scale: scale!, scaleWidth: scaleWidth! });
    return time;
  };

  return (
    <div
      className={`${prefix(...classNames)} ${(rowData?.classNames || []).join(
        ' ',
      )}`}
      style={style}
      onClick={(e) => {
        if (rowData && onClickRow) {
          const time = handleTime(e);
          if (time) {
            onClickRow(e, { row: rowData, time: time });
          }
        }
      }}
      onDoubleClick={(e) => {
        if (rowData && onDoubleClickRow) {
          const time = handleTime(e);
          if (time) {
            onDoubleClickRow(e, { row: rowData, time: time });
          }
        }
      }}
      onContextMenu={(e) => {
        if (rowData && onContextMenuRow) {
          const time = handleTime(e);
          if (time) {
            onContextMenuRow(e, { row: rowData, time: time });
          }
        }
      }}
    >
      {(rowData?.actions || []).map((action) => (
        <EditAction
          key={action.id}
          {...props}
          handleTime={handleTime}
          row={rowData!}
          action={action}
        />
      ))}
    </div>
  );
};
