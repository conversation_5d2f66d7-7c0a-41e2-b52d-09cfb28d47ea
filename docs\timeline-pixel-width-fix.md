# Timeline Pixel Width Calculation Fix

## Problem

The timeline event rendering was using incorrect pixel width calculations, leading to:
- Events showing as bars regardless of actual pixel width
- Diamond renderer not triggering for small events
- Inconsistent behavior across different zoom levels

## Root Cause

The renderers were using a simple multiplication `(action.end - action.start) * scale` instead of the proper timeline utility functions from `deal_data.ts`.

### Example of the Problem
```
Event Duration: 15 seconds
Scale: 100 
Incorrect Calculation: 15 * 100 = 1500px (way too large!)
```

This caused events to always appear large enough for bars, never triggering the diamond renderer.

## Solution

Updated all renderers to use the proper `parserTimeToTransform` function from `deal_data.ts`:

### 1. **Updated Interface**
```typescript
export interface NFMActionRendererProps {
  // ... existing props
  // Timeline parameters for proper pixel width calculation
  scaleWidth?: number;
  startLeft?: number;
}
```

### 2. **Fixed Pixel Width Calculation**
```typescript
// BEFORE: Incorrect calculation
const pixelWidth = (action.end - action.start) * scale;

// AFTER: Proper timeline calculation
const { width: pixelWidth } = parserTimeToTransform(
  { start: action.start, end: action.end },
  { startLeft, scale, scaleWidth }
);
```

### 3. **Updated All Renderers**

#### BaseActionRenderer
```typescript
export function BaseActionRenderer({
  action, row, scale, scaleWidth = 100, startLeft = 10, ...props
}) {
  const { left, width } = parserTimeToTransform(
    { start: action.start, end: action.end },
    { startLeft, scale, scaleWidth }
  );
  const actualWidth = Math.max(width, 20); // Minimum width
  // ... rest of renderer
}
```

#### getActionRenderer
```typescript
export function getActionRenderer(
  action: TimelineEvent,
  row: TimelineRow,
  scale: number = 100,
  scaleWidth: number = 100,
  startLeft: number = 10
): React.ComponentType<NFMActionRendererProps> {
  const { width: pixelWidth } = parserTimeToTransform(
    { start: action.start, end: action.end },
    { startLeft, scale, scaleWidth }
  );
  
  // Diamond renderer for events < 30px
  if (pixelWidth < 30) {
    return DiamondEventRenderer;
  }
  // ... severity-based selection
}
```

#### DiamondEventRenderer
```typescript
export function DiamondEventRenderer(props: NFMActionRendererProps) {
  const { left: leftPosition } = parserTimeToTransform(
    { start: action.start, end: action.start },
    { startLeft: props.startLeft || 10, scale, scaleWidth: props.scaleWidth || 100 }
  );
  // ... proper positioning
}
```

### 4. **Updated Timeline Parameters Passing**

#### NFMTimelineEditor
```typescript
const handleEventRender = useCallback((event: TimelineEvent, row: TimelineRow) => {
  return renderTimelineAction(event, row, {
    scale,
    height: finalConfig.rowHeight || 40,
    // Timeline parameters for proper pixel width calculation
    scaleWidth: timelineStaticConfig.scaleWidth,
    startLeft: timelineStaticConfig.startLeft,
  });
}, [scale, finalConfig.rowHeight, timelineStaticConfig.scaleWidth, timelineStaticConfig.startLeft]);
```

#### renderTimelineAction
```typescript
export function renderTimelineAction(action, row, props) {
  const Renderer = getActionRenderer(
    action, 
    row, 
    props.scale, 
    props.scaleWidth || 100, 
    props.startLeft || 10
  );
  return <Renderer action={action} row={row} {...props} />;
}
```

## Expected Results

### Before Fix
```
Event: 15 second duration
Scale: 100
Incorrect Width: 1500px → Always shows bar
```

### After Fix
```
Event: 15 second duration  
Scale: 100, ScaleWidth: 100, StartLeft: 10
Correct Width: ~25px → Shows diamond renderer
```

## Testing

### Console Output Should Show
```
[getActionRenderer] Event: abc123 Start: 10 End: 25 PixelWidth: 25
[getActionRenderer] Using DiamondEventRenderer for short event
[DiamondEventRenderer] Rendering diamond for event: abc123 at position: 110
```

### Visual Results
- ✅ **Short events** (< 30px) show diamond shapes only
- ✅ **Long events** show bars with proper width
- ✅ **Severity events** show appropriate colors and icons
- ✅ **Zoom changes** properly affect renderer selection

## Key Timeline Parameters

From `timelineStaticConfig`:
- **scaleWidth**: 100 (pixels per scale unit)
- **startLeft**: 10 (left offset in pixels)
- **scale**: Variable (time scale from props)

The `parserTimeToTransform` function uses these to calculate:
- **left**: Horizontal position in pixels
- **width**: Width in pixels

## Files Modified

- `types/timelineEditor.ts`: Added scaleWidth/startLeft to NFMActionRendererProps
- `components/timeline/NFMTimelineEditor.tsx`: Pass timeline parameters to renderer
- `components/timeline/effects/eventRenderers.tsx`: 
  - Import parserTimeToTransform
  - Update all renderers to use proper calculation
  - Fix DiamondEventRenderer positioning

This fix ensures that the timeline event rendering is based on actual pixel dimensions rather than incorrect time-based calculations.
