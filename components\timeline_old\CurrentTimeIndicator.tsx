import React, { useState, useRef, useCallback, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { type UseTimelineScaleReturn } from '@/hooks/useTimelineScale'

export interface CurrentTimeIndicatorProps {
  timeline: UseTimelineScaleReturn
  className?: string
  height?: number
  onSeek?: (time: number) => void
  onScrub?: (time: number) => void // Real-time scrubbing callback
}

export function CurrentTimeIndicator({
  timeline,
  className,
  height = 200,
  onSeek,
  onScrub
}: CurrentTimeIndicatorProps) {
  const { currentTime, pixelsPerSecond, scrollLeft, viewportWidth, duration, maxScrollLeft } = timeline
  const [isDragging, setIsDragging] = useState(false)
  const [dragTime, setDragTime] = useState(currentTime)
  const [isMouseDown, setIsMouseDown] = useState(false)
  const draggingRef = useRef(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const lastScrubTime = useRef(0)
  const scrubThrottleMs = 100 // Throttle scrubbing to every 100ms
  
  // Simplified position calculation
  const displayTime = isDragging ? dragTime : currentTime
  const position = displayTime * pixelsPerSecond - scrollLeft
  const isVisible = position >= -100 && position <= viewportWidth + 100

  // Auto-scroll timeline when dragging near edges
  const autoScrollTimeline = useCallback((mouseX: number, timelineWidth: number) => {
    const EDGE_THRESHOLD = 50
    const SCROLL_SPEED = 2
    
    if (mouseX < EDGE_THRESHOLD && scrollLeft > 0) {
      // Scroll left
      const newScrollLeft = Math.max(0, scrollLeft - SCROLL_SPEED * 10)
      timeline.setScrollLeft(newScrollLeft)
    } else if (mouseX > timelineWidth - EDGE_THRESHOLD && scrollLeft < maxScrollLeft) {
      // Scroll right
      const newScrollLeft = Math.min(maxScrollLeft, scrollLeft + SCROLL_SPEED * 10)
      timeline.setScrollLeft(newScrollLeft)
    }
  }, [scrollLeft, maxScrollLeft, timeline])

  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    setIsMouseDown(true)
    setIsDragging(true)
    draggingRef.current = true
    setDragTime(currentTime)

    const timelineContainer = document.querySelector('[data-timeline-container]') as HTMLElement
    if (!timelineContainer) return

    const containerRect = timelineContainer.getBoundingClientRect()
    const labelWidth = 64

    const handleMouseMove = (e: MouseEvent) => {
      if (!draggingRef.current || !isMouseDown) return
      
      // Calculate relative position within timeline (excluding label area)
      const relativeX = e.clientX - containerRect.left - labelWidth
      
      // Auto-scroll if near edges
      autoScrollTimeline(relativeX, containerRect.width - labelWidth)
      
      // Calculate time - use current scroll position for real-time updates
      const time = (relativeX + timeline.scrollLeft) / pixelsPerSecond
      const clampedTime = Math.max(0, Math.min(time, duration))

      setDragTime(clampedTime)

      // Throttled real-time scrubbing
      const now = Date.now()
      if (onScrub && now - lastScrubTime.current > scrubThrottleMs) {
        onScrub(clampedTime)
        lastScrubTime.current = now
      }
    }

    const handleMouseUp = (e: MouseEvent) => {
      e.preventDefault()
      
      if (draggingRef.current && isMouseDown) {
        draggingRef.current = false
        setIsDragging(false)
        setIsMouseDown(false)
        onSeek?.(dragTime) // Seek to final position
      }
      
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [currentTime, pixelsPerSecond, duration, dragTime, onSeek, isMouseDown, autoScrollTimeline, timeline])

  // Reset dragging state when mouse is released outside
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (isDragging) {
        setIsDragging(false)
        setIsMouseDown(false)
        draggingRef.current = false
      }
    }

    document.addEventListener('mouseup', handleGlobalMouseUp)
    return () => document.removeEventListener('mouseup', handleGlobalMouseUp)
  }, [isDragging])

  if (!isVisible) return null

  return (
    <div
      ref={containerRef}
      className={cn(
        "absolute top-0 z-30 cursor-grab select-none",
        isDragging && "cursor-grabbing",
        className
      )}
      style={{
        left: position,
        height: height,
        transition: isDragging ? 'none' : 'left 0.1s ease-out'
      }}
      onMouseDown={handleMouseDown}
      data-timeline-indicator
    >
      {/* Time indicator line */}
      <div className="w-0.5 h-full bg-red-500 shadow-lg pointer-events-none" />
      
      {/* Top triangle indicator - larger hit area */}
      <div className="absolute -top-2 -left-2 w-4 h-4 flex items-center justify-center">
        <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-transparent border-t-red-500" />
      </div>
      
      {/* Time label */}
      <div className={cn(
        "absolute -top-8 -left-8 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-lg font-mono whitespace-nowrap pointer-events-none",
        isDragging && "bg-red-600"
      )}>
        {formatTime(displayTime)}
      </div>
    </div>
  )
}

function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}
