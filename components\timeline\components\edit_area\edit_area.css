.timeline-editor:hover .timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar {
  height: 8px; /* Increased from 4px for better visibility */
}

.timeline-editor-edit-area {
  flex: 1 1 auto;
  margin-top: 10px;
  overflow-x: auto !important; /* Enable horizontal scrolling for timeline navigation */
  overflow-y: hidden !important; /* Disable vertical scrolling */
  position: relative;
}

.timeline-editor-edit-area .ReactVirtualized__Grid {
  outline: none !important;
  overflow-x: auto !important; /* Enable scrolling for timeline navigation */
  overflow-y: hidden !important; /* DIsable vertical scrolling for timeline navigation */

}

.timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar {
  width: 0px; /* Hide vertical scrollbar since we size to content */
  height: 8px; /* Show horizontal scrollbar for timeline scrubbing */
}

.timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1) !important;
  border-radius: 4px;
}

.timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* Dark mode scrollbar */
.dark .timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar-track {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.dark .timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar-thumb {
  background: #9ca3af;
  border-color: rgba(0, 0, 0, 0.2);
}

.dark .timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar-thumb:hover {
  background: #d1d5db;
}
