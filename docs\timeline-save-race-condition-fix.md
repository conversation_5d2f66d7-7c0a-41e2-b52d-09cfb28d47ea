# Timeline Save Race Condition Fix

## Problem Description

After enabling `enableChangeTracking: true`, the timeline was experiencing a race condition where:

1. User moves an event (e.g., drag to new position)
2. Change is detected and saved to Convex successfully
3. Convex query refreshes with the updated data from database
4. **Timeline reverts to original position** despite successful save

## Root Cause

The issue was in the `useTimelineData` hook's data synchronization logic:

```typescript
// This useEffect runs every time Convex data changes
useEffect(() => {
  // This overwrites local editorData with fresh database data
  setEditorData(timelineData.editorData);
  
  // This resets change tracking
  originalDataRef.current = deepClone(timelineData.editorData);
  setIsDirty(false);
  setHasUnsavedChanges(false);
  setChangeCount(0);
}, [timelineData, enableUndo]);
```

**Race Condition Sequence:**
1. User drags event → `editorData` updated locally
2. Save triggered → Convex mutation succeeds
3. Convex query refreshes → `timelineData` changes
4. `useEffect` runs → **overwrites** `editorData` with "fresh" data
5. Timeline reverts because the fresh data might not include the latest changes yet

## Solution

Added a saving state flag to prevent data overwrites during save operations:

### 1. Added Saving State Tracking

```typescript
// Refs for tracking
const originalDataRef = useRef<TimelineRow[]>([]);
const autoSaveTimeoutRef = useRef<NodeJS.Timeout>(null);
const debounceTimeoutRef = useRef<NodeJS.Timeout>(null);
const isSavingRef = useRef<boolean>(false); // NEW: Track saving state
```

### 2. Protected Data Refresh During Saves

```typescript
// Update state when timeline data changes
useEffect(() => {
  // Don't update state if we're currently saving (prevents race condition)
  if (isSavingRef.current) {
    console.log('[useTimelineData] Ignoring data change - save in progress');
    return;
  }
  
  console.log('[useTimelineData] External data change detected, updating state');
  setEditorData(timelineData.editorData);
  // ... rest of update logic
}, [timelineData, enableUndo]);
```

### 3. Updated Save Function

```typescript
const saveChanges = useCallback(async () => {
  try {
    console.log('Saving timeline changes...');
    
    // Set saving flag to prevent race condition with data refresh
    isSavingRef.current = true;

    // Get the changes that need to be persisted
    const changes = getChanges();

    if (changes.hasChanges) {
      // ... perform Convex mutations
    }

    // Update tracking state - keep current editorData as the new baseline
    console.log('[useTimelineData] Updating baseline after successful save');
    originalDataRef.current = deepClone(editorData);
    setIsDirty(false);
    setHasUnsavedChanges(false);
    setLastSaved(new Date());

    console.log('Timeline changes saved successfully');
  } catch (error) {
    console.error('Failed to save timeline changes:', error);
    throw error;
  } finally {
    // Clear saving flag after a short delay to allow Convex to propagate changes
    setTimeout(() => {
      isSavingRef.current = false;
    }, 500); // 500ms delay to allow database propagation
  }
}, [/* dependencies */]);
```

## Key Changes

### 1. **Saving State Protection**
- Added `isSavingRef` to track when a save operation is in progress
- Data refresh is blocked during save operations to prevent overwrites

### 2. **Delayed Flag Clearing**
- The saving flag is cleared after 500ms to allow Convex database propagation
- This ensures the next data refresh contains the saved changes

### 3. **Enhanced Logging**
- Added detailed logging to track data changes and save operations
- Helps debug any remaining synchronization issues

## Expected Behavior After Fix

1. ✅ User moves event → Local state updates immediately
2. ✅ Save operation begins → `isSavingRef.current = true`
3. ✅ Convex mutation succeeds → Changes persisted to database
4. ✅ Data refresh attempts → **Blocked** by saving flag
5. ✅ 500ms delay → Allows database propagation
6. ✅ Saving flag cleared → `isSavingRef.current = false`
7. ✅ Next data refresh → Contains the saved changes, no revert

## Testing Instructions

### Manual Testing
1. Open timeline with events
2. Enable editing mode
3. Drag an event to a new position
4. Observe console logs:
   - Should see "Saving timeline changes..."
   - Should see "Timeline changes saved: X updated"
   - Should see "Ignoring data change - save in progress" during save
   - Should NOT see timeline revert to original position

### Console Log Monitoring
Look for these log patterns:

```
✅ Good Pattern:
[useTimelineData] Handling data change
Saving timeline changes...
[useTimelineData] Ignoring data change - save in progress
Timeline changes saved: 1 updated
[useTimelineData] Updating baseline after successful save

❌ Bad Pattern (if still occurring):
[useTimelineData] Handling data change
Saving timeline changes...
[useTimelineData] External data change detected, updating state
Timeline changes saved: 1 updated
```

## Fallback Considerations

If the race condition persists, consider these additional approaches:

1. **Optimistic Updates**: Update the baseline immediately when changes are made
2. **Change Comparison**: Compare incoming data with current state before overwriting
3. **Debounced Refresh**: Add debouncing to data refresh operations
4. **State Reconciliation**: Implement smart merging of local and remote changes

## Files Modified

- `hooks/useTimelineData.ts`: Added saving state protection and enhanced logging

This fix should resolve the timeline revert issue while maintaining proper data synchronization with the Convex backend.
