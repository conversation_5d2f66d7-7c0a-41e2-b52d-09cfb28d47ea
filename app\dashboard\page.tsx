"use client"

import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"

import {
  Activity,
  Brain,
  Users,
  Clock,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import Link from "next/link"

export default function DashboardPage() {
  // Get real data from Convex
  const projects = useQuery(api.projects.getProjectswithPatientAndTeam, { limit: 10})
  const activeCount = useQuery(api.projects.getActiveProjectsCount)
  //const todaysProjects = useQuery(api.projects.getTodaysProjects)
  const patients = useQuery(api.patients.getPatients, { limit: 5 })

  const dashboardStats = {
    activeSessions: activeCount || 0,
    totalPatients: patients?.length || 0,
    completedToday: projects?.filter(p => {
      const today = new Date()
      const projectDate = p.actualEnd ? new Date(p.actualEnd) : null
      return projectDate && projectDate.toDateString() === today.toDateString() && p.status === "completed"
    }).length || 0,
    pendingReviews: projects?.filter(p => p.status === "completed").length || 0
  }

  const recentSessions = projects?.slice(0, 3).map(project => ({
    id: project._id,
    patientName: `${project.patient?.firstName} ${project.patient?.lastName}`,
    procedure: project.surgeryType || "Unknown Procedure",
    status: project.status,
    startTime: project.actualStart ? 
      new Date(project.actualStart).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
      new Date(project.scheduledStart).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    duration: project.actualEnd && project.actualStart ?
      `${Math.round((project.actualEnd - project.actualStart) / (1000 * 60))}m` : ('unknown')// :`${Math.round(project.estimatedDuration)}m (est)`
  })) || []

  if (projects === undefined || patients === undefined) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-muted-foreground">Loading dashboard...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Welcome back, Dr. Johnson</h1>
        <p className="text-muted-foreground">
          {"Here's what's happening in your neural monitoring suite today."}
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.activeSessions}</div>
            <p className="text-xs text-muted-foreground">
              +2 from yesterday
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.totalPatients}</div>
            <p className="text-xs text-muted-foreground">
              This week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.completedToday}</div>
            <p className="text-xs text-muted-foreground">
              +12% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.pendingReviews}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Sessions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Sessions</CardTitle>
          <CardDescription>
            Latest neural monitoring sessions and their current status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentSessions.map((session) => (
              <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="shrink-0">
                    <Brain className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{session.patientName}</p>
                    <p className="text-sm text-muted-foreground">{session.procedure}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{session.startTime}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">{session.duration}</p>
                  </div>
                  
                  <Badge 
                    variant={session.status === "in-progress" ? "default" : "secondary"}
                  >
                    {session.status}
                  </Badge>
                  
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks and shortcuts for neural monitoring
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Link href="/dashboard/live-monitoring">
              <Button className="h-20 flex-col space-y-2 w-full">
                <Activity className="h-6 w-6" />
                <span>Live Monitoring</span>
              </Button>
            </Link>
            <Link href="/patients">
              <Button variant="outline" className="h-20 flex-col space-y-2 w-full">
                <Users className="h-6 w-6" />
                <span>Patient Search</span>
              </Button>
            </Link>
            <Link href="/projects">
              <Button variant="outline" className="h-20 flex-col space-y-2 w-full">
                <Brain className="h-6 w-6" />
                <span>Project Management</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
