/**
 * Timeline export/import functionality for NFM Timeline Editor
 */

import React, { useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { TimelineEvent, TimelineRow } from './interface/timeline';
import { Doc } from '@/convex/_generated/dataModel';
import { Download, Upload, FileText, FileJson, FileSpreadsheet } from 'lucide-react';
import { toast } from 'sonner';

export interface TimelineExportData {
  version: string;
  timestamp: string;
  projectId?: string;
  sessionId?: string;
  metadata: {
    duration: number;
    eventCount: number;
    modalityCount: number;
    exportedBy?: string;
    description?: string;
  };
  modalities: Doc<"modalityConfigs">[];
  events: Doc<"monitoringEvents">[];
  settings?: {
    scale?: number;
    visibleModalities?: string[];
    currentTime?: number;
  };
}

export interface NFMTimelineExportImportProps {
  modalities: Doc<"modalityConfigs">[];
  events: Doc<"monitoringEvents">[];
  projectId?: string;
  sessionId?: string;
  currentTime?: number;
  scale?: number;
  visibleModalities?: string[];
  onImport?: (data: TimelineExportData) => void;
  className?: string;
}

export function NFMTimelineExportImport({
  modalities,
  events,
  projectId,
  sessionId,
  currentTime = 0,
  scale = 100,
  visibleModalities = [],
  onImport,
  className
}: NFMTimelineExportImportProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export to JSON
  const exportToJSON = useCallback(() => {
    try {
      const exportData: TimelineExportData = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        projectId,
        sessionId,
        metadata: {
          duration: events.length > 0 
            ? Math.max(...events.map(e => e.endTime || e.startTime + 1))
            : 3600,
          eventCount: events.length,
          modalityCount: modalities.length,
          exportedBy: 'NFM Timeline Editor',
          description: `Timeline export from ${new Date().toLocaleDateString()}`
        },
        modalities,
        events,
        settings: {
          scale,
          visibleModalities,
          currentTime
        }
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `timeline-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Timeline exported successfully');
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export timeline');
    }
  }, [modalities, events, projectId, sessionId, currentTime, scale, visibleModalities]);

  // Export to CSV
  const exportToCSV = useCallback(() => {
    try {
      const headers = [
        'Event ID',
        'Title',
        'Description',
        'Event Type',
        'Severity',
        'Modality ID',
        'Modality Name',
        'Start Time (s)',
        'End Time (s)',
        'Duration (s)',
        'Created By',
        'Created At',
        'Location',
        'Review Status'
      ];

      const rows = events.map(event => {
        const modality = modalities.find(m => m._id === event.modalityId);
        const duration = (event.endTime || event.startTime + 1) - event.startTime;
        
        return [
          event._id,
          event.title || '',
          event.description || '',
          event.eventType || '',
          event.severity || '',
          event.modalityId || '',
          modality?.name || '',
          event.startTime.toString(),
          (event.endTime || event.startTime + 1).toString(),
          duration.toString(),
          event.createdBy || '',
          new Date(event._creationTime).toISOString(),
          event.location || '',
          event.reviewStatus || ''
        ];
      });

      const csvContent = [headers, ...rows]
        .map(row => row.map(cell => `"${cell.toString().replace(/"/g, '""')}"`).join(','))
        .join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `timeline-events-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Timeline events exported to CSV');
    } catch (error) {
      console.error('CSV export failed:', error);
      toast.error('Failed to export to CSV');
    }
  }, [events, modalities]);

  // Export summary report
  const exportSummaryReport = useCallback(() => {
    try {
      const totalDuration = events.length > 0 
        ? Math.max(...events.map(e => e.endTime || e.startTime + 1))
        : 0;

      const eventsByModality = modalities.map(modality => {
        const modalityEvents = events.filter(e => e.modalityId === modality._id);
        return {
          modality: modality.name,
          eventCount: modalityEvents.length,
          totalDuration: modalityEvents.reduce((sum, e) => 
            sum + ((e.endTime || e.startTime + 1) - e.startTime), 0
          )
        };
      });

      const eventsBySeverity = ['critical', 'alarm', 'warning', 'info', 'normal'].map(severity => ({
        severity,
        count: events.filter(e => e.severity === severity).length
      }));

      const report = `
# Timeline Summary Report
Generated: ${new Date().toLocaleString()}
Project ID: ${projectId || 'N/A'}
Session ID: ${sessionId || 'N/A'}

## Overview
- Total Events: ${events.length}
- Total Modalities: ${modalities.length}
- Timeline Duration: ${Math.floor(totalDuration / 60)}:${(totalDuration % 60).toString().padStart(2, '0')}
- Export Time: ${new Date().toISOString()}

## Events by Modality
${eventsByModality.map(item => 
  `- ${item.modality}: ${item.eventCount} events (${Math.floor(item.totalDuration / 60)}:${(item.totalDuration % 60).toString().padStart(2, '0')})`
).join('\n')}

## Events by Severity
${eventsBySeverity.map(item => 
  `- ${item.severity.charAt(0).toUpperCase() + item.severity.slice(1)}: ${item.count} events`
).join('\n')}

## Modalities Configuration
${modalities.map(modality => `
### ${modality.name}
- ID: ${modality._id}
- Display Name: ${modality.displayName || 'N/A'}
- Color: ${modality.colorCode || 'N/A'}
- Active: ${modality.isActive ? 'Yes' : 'No'}
- Description: ${modality.description || 'N/A'}
`).join('\n')}

## Recent Events (Last 10)
${events.slice(-10).map(event => `
- **${event.title}** (${event.eventType})
  - Time: ${Math.floor(event.startTime / 60)}:${(event.startTime % 60).toString().padStart(2, '0')} - ${Math.floor((event.endTime || event.startTime + 1) / 60)}:${((event.endTime || event.startTime + 1) % 60).toString().padStart(2, '0')}
  - Severity: ${event.severity}
  - Description: ${event.description || 'N/A'}
`).join('\n')}
      `.trim();

      const blob = new Blob([report], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `timeline-report-${new Date().toISOString().split('T')[0]}.md`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Timeline report exported');
    } catch (error) {
      console.error('Report export failed:', error);
      toast.error('Failed to export report');
    }
  }, [events, modalities, projectId, sessionId]);

  // Import from JSON
  const importFromJSON = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const importData: TimelineExportData = JSON.parse(content);

        // Validate import data
        if (!importData.version || !importData.modalities || !importData.events) {
          throw new Error('Invalid timeline export file format');
        }

        // Check version compatibility
        if (importData.version !== '1.0.0') {
          toast.warning(`Import file version ${importData.version} may not be fully compatible`);
        }

        onImport?.(importData);
        toast.success(`Imported ${importData.events.length} events and ${importData.modalities.length} modalities`);
      } catch (error) {
        console.error('Import failed:', error);
        toast.error('Failed to import timeline: ' + (error as Error).message);
      }
    };
    reader.readAsText(file);
  }, [onImport]);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type === 'application/json' || file.name.endsWith('.json')) {
        importFromJSON(file);
      } else {
        toast.error('Please select a JSON file');
      }
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [importFromJSON]);

  const triggerFileSelect = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Export buttons */}
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={exportToJSON}
          title="Export timeline as JSON"
          className="h-8"
        >
          <Download className="w-4 h-4 mr-1" />
          JSON
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={exportToCSV}
          title="Export events as CSV"
          className="h-8"
        >
          <FileSpreadsheet className="w-4 h-4 mr-1" />
          CSV
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={exportSummaryReport}
          title="Export summary report"
          className="h-8"
        >
          <FileText className="w-4 h-4 mr-1" />
          Report
        </Button>
      </div>

      {/* Import button */}
      <Button
        variant="outline"
        size="sm"
        onClick={triggerFileSelect}
        title="Import timeline from JSON"
        className="h-8"
      >
        <Upload className="w-4 h-4 mr-1" />
        Import
      </Button>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".json,application/json"
        onChange={handleFileSelect}
        className="hidden"
      />
    </div>
  );
}
