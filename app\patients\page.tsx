"use client"

import { useState } from "react"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Search,
  User,
  Phone,
  Calendar,
  Plus,
  Brain,
  FileText,
  AlertTriangle
} from "lucide-react"
import Link from "next/link"

export default function PatientsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const patients = useQuery(api.patients.getPatients, { 
    search: searchQuery || undefined,
    limit: 100 
  })
  const activeCount = useQuery(api.projects.getActiveProjectsCount)

  const calculateAge = (dateOfBirth: number) => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString()
  }

  return (
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Patient Management</h1>
            <p className="text-muted-foreground">
              Manage patient information and medical records
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Patient
          </Button>
        </div>

        {/* Search Bar */}
        <Card>
          <CardContent className="pt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search patients by name or MRN..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{patients?.length || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Cases</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeCount || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New This Month</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {patients?.filter(p => {
                  const createdDate = new Date(p._creationTime)
                  const now = new Date()
                  return createdDate.getMonth() === now.getMonth() && 
                         createdDate.getFullYear() === now.getFullYear()
                }).length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Patients List */}
        <div className="space-y-4">
          {patients?.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold">No patients found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? 
                    `No patients match "${searchQuery}"` : 
                    "Get started by adding your first patient"
                  }
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Patient
                </Button>
              </CardContent>
            </Card>
          ) : (
            patients?.map((patient) => (
              <Card key={patient._id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">
                          {patient.firstName} {patient.lastName}
                        </CardTitle>
                        <CardDescription>
                          MRN: {patient.medicalRecordNumber} • Age: {calculateAge(patient.dateOfBirth)}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge variant="outline">
                      {patient.gender}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>DOB: {formatDate(patient.dateOfBirth)}</span>
                      </div>
                      {patient.contactPhone && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span>{patient.contactPhone}</span>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      {patient.emergencyContact && (
                        <div className="text-sm">
                          <span className="text-muted-foreground">Emergency: </span>
                          <span>{patient.emergencyContact.name} ({patient.emergencyContact.relationship})</span>
                        </div>
                      )}
                      {patient.allergies && (
                        <div className="flex items-center space-x-2 text-sm">
                          <AlertTriangle className="h-4 w-4 text-destructive" />
                          <span className="text-destructive font-medium">Allergies</span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-end space-x-2">
                      <Link href={`/patients/${patient._id}`}>
                        <Button variant="outline" size="sm">
                          <FileText className="mr-1 h-3 w-3" />
                          View Details
                        </Button>
                      </Link>
                      <Button size="sm">
                        <Brain className="mr-1 h-3 w-3" />
                        New Project
                      </Button>
                    </div>
                  </div>

                  {patient.medicalHistory && patient.medicalHistory.length > 0 && (
                    <div className="mt-4 p-3 bg-muted rounded-lg">
                      <p className="text-sm font-medium mb-1">Medical History:</p>
                      <div className="text-sm text-muted-foreground space-y-1">
                        {patient.medicalHistory.slice(0, 3).map((history, index) => (
                          <div key={index}>{history}</div>
                        ))}
                        {patient.medicalHistory.length > 3 && (
                          <div className="text-xs text-muted-foreground">
                            ...and {patient.medicalHistory.length - 3} more
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
  )
}