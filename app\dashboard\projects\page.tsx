"use client"

import { useState } from "react"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { MainLayout } from "@/components/layout/main-layout"
import { ProjectsTable } from "@/components/projects/ProjectsTable"
import { ProjectActionModal } from "@/components/projects/ProjectActionModal"
import { CreateProjectModal } from "@/components/projects/CreateProjectModal"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  Plus,
  Brain,
  Play,
  CalendarDays,
  CheckCircle
} from "lucide-react"

export default function ProjectsPage() {
  const [selectedProject, setSelectedProject] = useState<any | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showActionModal, setShowActionModal] = useState(false)
  
  const projects = useQuery(api.projects.getProjects, {})
  const activeCount = useQuery(api.projects.getActiveProjectsCount)

  // Mock user data - in real app this would come from authentication
  const user = {
    name: "Dr. <PERSON>",
    email: "<EMAIL>",
    role: "Neurologist",
  }

  const handleProjectSelect = (project: any) => {
    setSelectedProject(project)
    setShowActionModal(true)
  }

  const handleProjectAction = (projectId: string, action: string) => {
    console.log(`Action ${action} for project ${projectId}`)
    
    switch (action) {
      case "view":
        alert("Navigating to project details...")
        break
      case "start":
        alert("Starting live monitoring session...")
        break
      case "monitor":
        alert("Opening monitoring interface...")
        break
      case "edit":
        alert("Opening project editor...")
        break
      case "report":
        alert("Generating project report...")
        break
      default:
        console.log("Unknown action:", action)
    }
  }

  const handleModalAction = (action: string) => {
    if (selectedProject) {
      handleProjectAction(selectedProject._id, action)
    }
    setShowActionModal(false)
  }


  return (
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Surgery Projects</h1>
            <p className="text-muted-foreground">
              Manage neurophysiology monitoring sessions
            </p>
          </div>
          <Button onClick={() => setShowCreateModal(true)}>
            <Plus className="mr-2 h-4 w-4" />
            New Project
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{projects?.length || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
              <Play className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeCount || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled Today</CardTitle>
              <CalendarDays className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {projects?.filter(p => {
                  const today = new Date()
                  const projectDate = new Date(p.scheduledStart)
                  return projectDate.toDateString() === today.toDateString()
                }).length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {projects?.filter(p => p.status === "completed").length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Projects Table */}
        <ProjectsTable 
          projects={projects || []}
          onProjectSelect={handleProjectSelect}
          onProjectAction={handleProjectAction}
        />

        {/* Modals */}
        <ProjectActionModal
          isOpen={showActionModal}
          onClose={() => setShowActionModal(false)}
          onAction={handleModalAction}
          project={selectedProject}
        />
        
        <CreateProjectModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false)
            // Refresh projects list
          }}
        />
      </div>
  )
}