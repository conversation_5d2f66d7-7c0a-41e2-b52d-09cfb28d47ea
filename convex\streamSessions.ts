import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Get active session for a project
export const getActiveSession = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const activeSession = await ctx.db
      .query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.or(
        q.eq(q.field("status"), "live"),
        q.eq(q.field("status"), "starting"),
        q.eq(q.field("status"), "paused")
      ))
      .first();
    
    return activeSession;
  },
});

// Get a specific session
export const getSession = query({
  args: { sessionId: v.id("streamSessions") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.sessionId);
  },
});

// Get all sessions for a project
export const getProjectSessions = query({
  args: { 
    projectId: v.id("projects"),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const sessions = await ctx.db
      .query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("desc")
      .take(args.limit ?? 50);
    
    return sessions;
  },
});

// Create a new session
export const createSession = mutation({
  args: {
    projectId: v.id("projects"),
    streamSources: v.array(v.object({
      name: v.string(),
      url: v.string(),
      quality: v.string(),
      isActive: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const sessionId = await ctx.db.insert("streamSessions", {
      projectId: args.projectId,
      status: "starting",
      streamSources: args.streamSources,
      startTime: Date.now(),
      viewers: [],
      eventCount: 0,
    });
    
    return sessionId;
  },
});

// Start a session
export const startSession = mutation({
  args: { sessionId: v.id("streamSessions") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.sessionId, {
      status: "live",
      startTime: Date.now(),
    });
    
    return { success: true };
  },
});

// Pause a session
export const pauseSession = mutation({
  args: { sessionId: v.id("streamSessions") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.sessionId, {
      status: "paused",
    });
    
    return { success: true };
  },
});

// Stop a session
export const stopSession = mutation({
  args: { sessionId: v.id("streamSessions") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.sessionId, {
      status: "stopped",
      endTime: Date.now(),
    });
    
    return { success: true };
  },
});

// Add viewer to session
export const addViewer = mutation({
  args: {
    sessionId: v.id("streamSessions"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }
    
    const viewers = session.viewers || [];
    if (!viewers.includes(args.userId)) {
      await ctx.db.patch(args.sessionId, {
        viewers: [...viewers, args.userId],
      });
    }
    
    return { success: true };
  },
});

// Remove viewer from session
export const removeViewer = mutation({
  args: {
    sessionId: v.id("streamSessions"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }
    
    const viewers = session.viewers || [];
    await ctx.db.patch(args.sessionId, {
      viewers: viewers.filter(id => id !== args.userId),
    });
    
    return { success: true };
  },
});

// Update session event count
export const updateEventCount = mutation({
  args: {
    sessionId: v.id("streamSessions"),
    eventCount: v.number(),
    lastEventTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.sessionId, {
      eventCount: args.eventCount,
      lastEventTime: args.lastEventTime,
    });
    
    return { success: true };
  },
});

// Get or create active session for a project
export const getOrCreateActiveSession = mutation({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    // First, try to find an existing active session
    const activeSession = await ctx.db
      .query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.or(
        q.eq(q.field("status"), "live"),
        q.eq(q.field("status"), "starting"),
        q.eq(q.field("status"), "paused")
      ))
      .first();
    
    if (activeSession) {
      return activeSession._id;
    }
    
    // If no active session exists, create a new one
    const sessionId = await ctx.db.insert("streamSessions", {
      projectId: args.projectId,
      status: "starting",
      streamSources: [],
      startTime: Date.now(),
      viewers: [],
      eventCount: 0,
    });
    
    return sessionId;
  },
});
