/**
 * Context menu for timeline events and rows
 */

import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { TimelineEvent, TimelineRow } from './interface/timeline';
import {
  Edit3,
  Trash2,
  <PERSON><PERSON>,
  Sciss<PERSON>,
  Play,
  Pause,
  ZoomIn,
  Info,
  Clock,
  Tag,
  Bookmark
} from 'lucide-react';

export interface ContextMenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  disabled?: boolean;
  danger?: boolean;
  separator?: boolean;
  onClick?: () => void;
}

export interface NFMTimelineContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number };
  target?: {
    type: 'event' | 'row' | 'timeline';
    event?: TimelineEvent;
    row?: TimelineRow;
    time?: number;
  };
  onClose: () => void;
  onEdit?: (event: TimelineEvent) => void;
  onDelete?: (event: TimelineEvent) => void;
  onDuplicate?: (event: TimelineEvent) => void;
  onCopy?: (event: TimelineEvent) => void;
  onCut?: (event: TimelineEvent) => void;
  onPlayFrom?: (time: number) => void;
  onZoomToEvent?: (event: TimelineEvent) => void;
  onCreateEvent?: (time: number, rowId?: string) => void;
  onEventInfo?: (event: TimelineEvent) => void;
  className?: string;
}

const NFMTimelineContextMenuComponent = React.memo(function NFMTimelineContextMenu({
  isOpen,
  position,
  target,
  onClose,
  onEdit,
  onDelete,
  onDuplicate,
  onCopy,
  onCut,
  onPlayFrom,
  onZoomToEvent,
  onCreateEvent,
  onEventInfo,
  className
}: NFMTimelineContextMenuProps) {
  // Only log when actually open to reduce spam
  if (isOpen) {
    console.debug('[NFMTimelineContextMenu] Rendered with target:', target);
  }
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu on outside click
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  // Generate menu items based on target
  const menuItems: ContextMenuItem[] = React.useMemo(() => {
    if (!target) return [];

    const items: ContextMenuItem[] = [];

    if (target.type === 'event' && target.event) {
      const event = target.event;
      
      // Event-specific actions
      items.push(
        {
          id: 'edit',
          label: 'Edit Event',
          icon: Edit3,
          shortcut: 'Enter',
          onClick: () => onEdit?.(event)
        },
        {
          id: 'info',
          label: 'Event Info',
          icon: Info,
          shortcut: 'I',
          onClick: () => onEventInfo?.(event)
        },
        { id: 'sep1', label: '', separator: true },
        {
          id: 'copy',
          label: 'Copy',
          icon: Copy,
          shortcut: 'Ctrl+C',
          onClick: () => onCopy?.(event)
        },
        {
          id: 'cut',
          label: 'Cut',
          icon: Scissors,
          shortcut: 'Ctrl+X',
          onClick: () => onCut?.(event)
        },
        {
          id: 'duplicate',
          label: 'Duplicate',
          icon: Bookmark,
          shortcut: 'Ctrl+D',
          onClick: () => onDuplicate?.(event)
        },
        { id: 'sep2', label: '', separator: true },
        {
          id: 'play-from',
          label: 'Play from Here',
          icon: Play,
          shortcut: 'Space',
          onClick: () => onPlayFrom?.(event.start)
        },
        {
          id: 'zoom-to',
          label: 'Zoom to Event',
          icon: ZoomIn,
          shortcut: 'Z',
          onClick: () => onZoomToEvent?.(event)
        },
        { id: 'sep3', label: '', separator: true },
        {
          id: 'delete',
          label: 'Delete Event',
          icon: Trash2,
          shortcut: 'Del',
          danger: true,
          onClick: () => onDelete?.(event)
        }
      );
    } else if (target.type === 'row' && target.row) {
      const row = target.row;
      
      // Row-specific actions
      items.push(
        {
          id: 'create-event',
          label: 'Create Event Here',
          icon: Tag,
          onClick: () => target.time !== undefined && onCreateEvent?.(target.time, row.id)
        },
        { id: 'sep1', label: '', separator: true },
        {
          id: 'play-from',
          label: 'Play from Here',
          icon: Play,
          shortcut: 'Space',
          onClick: () => target.time !== undefined && onPlayFrom?.(target.time)
        }
      );
    } else if (target.type === 'timeline') {
      // Timeline background actions
      items.push(
        {
          id: 'create-event',
          label: 'Create Event',
          icon: Tag,
          onClick: () => target.time !== undefined && onCreateEvent?.(target.time)
        },
        {
          id: 'play-from',
          label: 'Play from Here',
          icon: Play,
          shortcut: 'Space',
          onClick: () => target.time !== undefined && onPlayFrom?.(target.time)
        }
      );
    }

    return items.filter(item => item.separator || item.onClick); // Remove items without handlers
  }, [target, onEdit, onDelete, onDuplicate, onCopy, onCut, onPlayFrom, onZoomToEvent, onCreateEvent, onEventInfo]);

  if (!isOpen || menuItems.length === 0) {
    return null;
  }

  return (
    <div
      ref={menuRef}
      className={cn(
        'fixed z-50 min-w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
        'rounded-md shadow-lg py-1 animate-in fade-in-0 zoom-in-95',
        className
      )}
      style={{
        left: position.x,
        top: position.y,
        // Ensure menu stays within viewport
        transform: 'translate(0, 0)',
        maxHeight: '80vh',
        overflowY: 'auto'
      }}
    >
      {menuItems.map((item) => {
        if (item.separator) {
          return (
            <div
              key={item.id}
              className="h-px bg-gray-200 dark:bg-gray-700 my-1"
            />
          );
        }

        const Icon = item.icon;

        return (
          <button
            key={item.id}
            className={cn(
              'w-full px-3 py-2 text-left text-sm flex items-center gap-3',
              'hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              {
                'text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20': item.danger,
                'text-gray-900 dark:text-gray-100': !item.danger
              }
            )}
            disabled={item.disabled}
            onClick={() => {
              item.onClick?.();
              onClose();
            }}
          >
            {Icon && <Icon className="w-4 h-4 flex-shrink-0" />}
            <span className="flex-1">{item.label}</span>
            {item.shortcut && (
              <span className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                {item.shortcut}
              </span>
            )}
          </button>
        );
      })}
    </div>
  );
});

// Export the memoized component
export const NFMTimelineContextMenu = NFMTimelineContextMenuComponent;

/**
 * Hook for managing context menu state
 */
export function useTimelineContextMenu() {
  const [contextMenu, setContextMenu] = React.useState<{
    isOpen: boolean;
    position: { x: number; y: number };
    target?: NFMTimelineContextMenuProps['target'];
  }>({
    isOpen: false,
    position: { x: 0, y: 0 }
  });

  const openContextMenu = React.useCallback((
    event: React.MouseEvent,
    target: NFMTimelineContextMenuProps['target']
  ) => {
    event.preventDefault();
    event.stopPropagation();

    // Calculate position to keep menu within viewport
    const rect = document.body.getBoundingClientRect();
    const x = Math.min(event.clientX, rect.width - 200); // 200px menu width
    const y = Math.min(event.clientY, rect.height - 300); // 300px max menu height

    setContextMenu({
      isOpen: true,
      position: { x, y },
      target
    });
  }, []);

  const closeContextMenu = React.useCallback(() => {
    setContextMenu(prev => ({ ...prev, isOpen: false }));
  }, []);

  return {
    contextMenu,
    openContextMenu,
    closeContextMenu
  };
}
