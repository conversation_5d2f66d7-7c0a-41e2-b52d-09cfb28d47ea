"use client";

import React, { useState, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/components/contexts/ProjectContext';
import { Filter, X, Clock, AlertTriangle, Activity, Zap } from 'lucide-react';
import { Id } from '@/convex/_generated/dataModel';

export interface TimelineFilterState {
  modalityIds: Id<"modalityConfigs">[];
  severities: ("normal" | "warning" | "critical")[];
  timeRange: {
    start?: number;
    end?: number;
  };
  eventTypes: string[];
}

interface TimelineFiltersProps {
  filters: TimelineFilterState;
  onFiltersChange: (filters: TimelineFilterState) => void;
  className?: string;
}

export function TimelineFilters({
  filters,
  onFiltersChange,
  className
}: TimelineFiltersProps) {
  const { modalities, events } = useProjectContext();
  const [isOpen, setIsOpen] = useState(false);

  // Get unique event types from current events
  const availableEventTypes = useMemo(() => {
    if (!events) return [];
    const types = new Set(events.map(e => e.eventType));
    return Array.from(types).sort();
  }, [events]);

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.modalityIds.length > 0) count++;
    if (filters.severities.length > 0) count++;
    if (filters.timeRange.start !== undefined || filters.timeRange.end !== undefined) count++;
    if (filters.eventTypes.length > 0) count++;
    return count;
  }, [filters]);

  // Handle modality filter change
  const handleModalityChange = useCallback((modalityId: Id<"modalityConfigs">, checked: boolean) => {
    const newModalityIds = checked
      ? [...filters.modalityIds, modalityId]
      : filters.modalityIds.filter(id => id !== modalityId);
    
    onFiltersChange({
      ...filters,
      modalityIds: newModalityIds
    });
  }, [filters, onFiltersChange]);

  // Handle severity filter change
  const handleSeverityChange = useCallback((severity: "normal" | "warning" | "critical", checked: boolean) => {
    const newSeverities = checked
      ? [...filters.severities, severity]
      : filters.severities.filter(s => s !== severity);
    
    onFiltersChange({
      ...filters,
      severities: newSeverities
    });
  }, [filters, onFiltersChange]);

  // Handle time range change
  const handleTimeRangeChange = useCallback((field: 'start' | 'end', value: string) => {
    const numValue = value ? parseFloat(value) : undefined;
    onFiltersChange({
      ...filters,
      timeRange: {
        ...filters.timeRange,
        [field]: numValue
      }
    });
  }, [filters, onFiltersChange]);

  // Handle event type change
  const handleEventTypeChange = useCallback((eventType: string, checked: boolean) => {
    const newEventTypes = checked
      ? [...filters.eventTypes, eventType]
      : filters.eventTypes.filter(t => t !== eventType);
    
    onFiltersChange({
      ...filters,
      eventTypes: newEventTypes
    });
  }, [filters, onFiltersChange]);

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    onFiltersChange({
      modalityIds: [],
      severities: [],
      timeRange: {},
      eventTypes: []
    });
  }, [onFiltersChange]);

  // Get severity icon and color
  const getSeverityInfo = (severity: "normal" | "warning" | "critical") => {
    switch (severity) {
      case 'critical':
        return { icon: AlertTriangle, color: 'text-red-600', bgColor: 'bg-red-100' };
      case 'warning':
        return { icon: Activity, color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
      case 'normal':
        return { icon: Zap, color: 'text-green-600', bgColor: 'bg-green-100' };
    }
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Filter Button */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="start">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Timeline Filters</h4>
              {activeFilterCount > 0 && (
                <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                  Clear All
                </Button>
              )}
            </div>

            <Separator />

            {/* Modality Filters */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Modalities</Label>
              <div className="space-y-2">
                {modalities?.map((modality) => (
                  <label key={modality._id} className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.modalityIds.includes(modality._id)}
                      onChange={(e) => handleModalityChange(modality._id, e.target.checked)}
                      className="rounded"
                    />
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: modality.colorCode }}
                    />
                    <span className="text-sm">{modality.displayName}</span>
                  </label>
                ))}
              </div>
            </div>

            <Separator />

            {/* Severity Filters */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Severity</Label>
              <div className="space-y-2">
                {(['normal', 'warning', 'critical'] as const).map((severity) => {
                  const { icon: Icon, color, bgColor } = getSeverityInfo(severity);
                  return (
                    <label key={severity} className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={filters.severities.includes(severity)}
                        onChange={(e) => handleSeverityChange(severity, e.target.checked)}
                        className="rounded"
                      />
                      <div className={cn("p-1 rounded", bgColor)}>
                        <Icon className={cn("h-3 w-3", color)} />
                      </div>
                      <span className="text-sm capitalize">{severity}</span>
                    </label>
                  );
                })}
              </div>
            </div>

            <Separator />

            {/* Time Range Filters */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-1">
                <Clock className="h-3 w-3" />
                Time Range (seconds)
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="start-time" className="text-xs">Start</Label>
                  <Input
                    id="start-time"
                    type="number"
                    placeholder="0"
                    value={filters.timeRange.start || ''}
                    onChange={(e) => handleTimeRangeChange('start', e.target.value)}
                    className="h-8"
                  />
                </div>
                <div>
                  <Label htmlFor="end-time" className="text-xs">End</Label>
                  <Input
                    id="end-time"
                    type="number"
                    placeholder="∞"
                    value={filters.timeRange.end || ''}
                    onChange={(e) => handleTimeRangeChange('end', e.target.value)}
                    className="h-8"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Event Type Filters */}
            {availableEventTypes.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Event Types</Label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {availableEventTypes.map((eventType) => (
                    <label key={eventType} className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={filters.eventTypes.includes(eventType)}
                        onChange={(e) => handleEventTypeChange(eventType, e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm">{eventType}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* Active Filter Badges */}
      {activeFilterCount > 0 && (
        <div className="flex items-center gap-1 flex-wrap">
          {filters.modalityIds.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {filters.modalityIds.length} Modalities
            </Badge>
          )}
          {filters.severities.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {filters.severities.length} Severities
            </Badge>
          )}
          {(filters.timeRange.start !== undefined || filters.timeRange.end !== undefined) && (
            <Badge variant="secondary" className="text-xs">
              Time Range
            </Badge>
          )}
          {filters.eventTypes.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {filters.eventTypes.length} Types
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
