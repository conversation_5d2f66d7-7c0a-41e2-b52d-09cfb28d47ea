import { TimelineEffect, TimelineRow } from '@xzdarcy/react-timeline-editor';

export const mockEffect: Record<string, TimelineEffect> = {
  effect0: {
    id: "effect0",
    name: "效果0",
  },
  effect1: {
    id: "effect1",
    name: "效果1",
  },
};


export const mockData: TimelineRow[] = [
  {
    id: "0",
    actions: [],
  },
  {
    id: "1",
    actions: [],
  },
  {
    id: "2",
    actions: [],
  },
  {
    id: "3",
    actions: [],
  },
  {
    id: "4",
    actions: [],
  },
  {
    id: "5",
    actions: [],
  },
  {
    id: "6",
    actions: [],
  },
  {
    id: "7",
    actions: [],
  },
];
